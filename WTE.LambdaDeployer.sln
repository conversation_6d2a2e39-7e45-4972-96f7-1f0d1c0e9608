﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
VisualStudioVersion = 15.0.27130.2010
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WTE.LambdaDeployer", "src\WTE.LambdaDeployer\WTE.LambdaDeployer.csproj", "{FAB9B60B-AE3B-4A60-BF8B-CE1BA7250DFF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WTE.LambdaDeployer.Common", "src\WTE.LambdaDeployer.Common\WTE.LambdaDeployer.Common.csproj", "{AF69F0CF-CC68-4E02-B3A9-FF369A1CA9F6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{99A733DA-85D3-4B20-981F-A438B4EF744D}"
	ProjectSection(SolutionItems) = preProject
		lambdaConfig.json = lambdaConfig.json
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FAB9B60B-AE3B-4A60-BF8B-CE1BA7250DFF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FAB9B60B-AE3B-4A60-BF8B-CE1BA7250DFF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FAB9B60B-AE3B-4A60-BF8B-CE1BA7250DFF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FAB9B60B-AE3B-4A60-BF8B-CE1BA7250DFF}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF69F0CF-CC68-4E02-B3A9-FF369A1CA9F6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF69F0CF-CC68-4E02-B3A9-FF369A1CA9F6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF69F0CF-CC68-4E02-B3A9-FF369A1CA9F6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF69F0CF-CC68-4E02-B3A9-FF369A1CA9F6}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {874F08B9-33C9-4E22-84C5-5F6C7D0ADD24}
	EndGlobalSection
EndGlobal
