using System;
using System.Collections.Generic;
using System.Text;
using WTE.LambdaDeployer.Common.Enums;

namespace WTE.LambdaDeployer.Common.ConfigModel
{
    public class LambdaSettings
    {
        public string Name { get; set; }
        public string Method { get; set; }
        public string Runtime { get; set; }
        public int? Memory { get; set; }
        public int? Timeout { get; set; }
        public string GroupName { get; set; }
        public Dictionary<string, string> EnvironmentVariables { get; set; } = new Dictionary<string, string>();

        // Nodejs specific
        public List<string> Include { get; set; }

        // C# specific
        public string Framework { get; set; }
        public string AssemblyName { get; set; }
        public string DeclaringType { get; set; }

        // Runtime
        public bool IsNewFunction { get; set; }
        public string Version { get; set; }
    }
}