using System;
using System.Collections.Generic;
using System.Text;
using WTE.LambdaDeployer.Common.Enums;

namespace WTE.LambdaDeployer.Common.ConfigModel
{
    public static class RollbackStageConfig
    {
        public static EnvironmentStage GetRollbackStage(EnvironmentStage stage)
        {
            switch (stage)
            {
                case EnvironmentStage.staging:
                    return EnvironmentStage.staging_rollback;
                case EnvironmentStage.prod:
                    return EnvironmentStage.prod_rollback;
                default:
                    return EnvironmentStage.NONE;
            }
        }
    }
}