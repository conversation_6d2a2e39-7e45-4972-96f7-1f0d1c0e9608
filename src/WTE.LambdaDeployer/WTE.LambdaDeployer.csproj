<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ApplicationIcon />
    <StartupObject>WTE.LambdaDeployer.Program</StartupObject>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>TRACE;DEBUG</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AWSSDK.Lambda" Version="3.7.402.10" />
    <PackageReference Include="AWSSDK.SimpleNotificationService" Version="3.7.400.20" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\WTE.LambdaDeployer.Common\WTE.LambdaDeployer.Common.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="ResultReport\" />
  </ItemGroup>
</Project>