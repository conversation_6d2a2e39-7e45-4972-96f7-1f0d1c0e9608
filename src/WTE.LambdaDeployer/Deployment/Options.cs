using System;
using System.Collections.Generic;
using System.Text;
using WTE.LambdaDeployer.Common.ConfigModel;
using WTE.LambdaDeployer.Common.Enums;

namespace WTE.LambdaDeployer.Deployment
{
    public class Options
    {
        public string LambdaDirectory { get; set; }
        public bool ScanAlias { get; set; }
        public bool DeployAll { get; set; }
        public string AWSProfileName { get; set; }
        public bool HighPrivilege { get; set; }
        public List<LambdaDefinition> LambdasToDeploy { get; set; } = new List<LambdaDefinition>();
        public List<LambdaLanguage> DeployAllLanguage { get; set; } = new List<LambdaLanguage>();
        public bool Verbose { get; set; } = false;
        public bool NotReally { get; set; } = false;
        public bool PromoteStage { get; set; } = false;
        public bool DeployMode { get; set; } = false;
        public EnvironmentStage TargetStage { get; set; } = EnvironmentStage.NONE;
        public EnvironmentStage SourceStage { get; set; }
        public EnvironmentStage BackupStage { get; set; }
        public int InterimSleepMs { get; set; } = 20000;
        public int MaxParallelDeployments { get; set; } = 5;
        public bool CreateMissingDefaults { get; set; } = false;
        public bool CustomStage { get; set; } = false;
        public bool CustomStageBackup { get; set; } = false;
        public string CustomStageTargetName { get; set; }
        public string CustomStageSourceName { get; set; }
        public string CustomStageBackupName { get; set; }
        public int? Memory { get; set; }
        public int? Timeout { get; set; }
        public Dictionary<string, string> AdditionalVariables { get; set; } = new Dictionary<string, string>();

        // Add a method to parse the max parallel deployments from command line
        public void SetMaxParallelDeployments(string value)
        {
            if (int.TryParse(value, out int maxParallel) && maxParallel > 0)
            {
                MaxParallelDeployments = maxParallel;
            }
            else
            {
                Console.WriteLine($"Invalid value for max parallel deployments: {value}. Using default: {MaxParallelDeployments}");
            }
        }

        public bool HasTargetStage()
        {
            // Existing implementation
            return true; // Placeholder - keep the actual implementation
        }

        public string GetTargetStage()
        {
            // Existing implementation
            return ""; // Placeholder - keep the actual implementation
        }

        public string GetSourceStage()
        {
            // Existing implementation
            return ""; // Placeholder - keep the actual implementation
        }

        public bool BackupRequired()
        {
            // Existing implementation
            return false; // Placeholder - keep the actual implementation
        }

        public string GetBackupStage()
        {
            // Existing implementation
            return ""; // Placeholder - keep the actual implementation
        }

        public bool ShouldDeployTarget()
        {
            // Existing implementation
            return false; // Placeholder - keep the actual implementation
        }
    }
}
