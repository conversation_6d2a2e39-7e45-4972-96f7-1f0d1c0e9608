using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using WTE.LambdaDeployer.Common.ConfigModel;
using WTE.LambdaDeployer.Common.Enums;
using WTE.LambdaDeployer.Reporting;

namespace WTE.LambdaDeployer.Deployment
{
    public static class CSharpDeployer
    {
        public static void DeployAll(Options options, LambdaConfig config = null)
        {
            BaseDeployer.DeployAll(options, LambdaLanguage.CSharp, "C#", DeploySingle, config);
        }

        public static List<LambdaSettings> GetLambdaSettings(Options options, LambdaConfig config = null)
        {
            return BaseDeployer.GetLambdaSettings(options, LambdaLanguage.CSharp, "C#", config);
        }

        public static void Deploy(Options options, LambdaConfig config = null, List<LambdaDefinition> specificLambdas = null)
        {
            BaseDeployer.Deploy(options, LambdaLanguage.CSharp, "C#", DeploySingle, config, specificLambdas);
        }

        public static void DeploySingle(LambdaSettings lambda, Options options)
        {
            try
            {
                if (lambda == null)
                {
                    throw new ArgumentNullException(nameof(lambda), "Lambda settings cannot be null");
                }

                Console.WriteLine(
                    $"{lambda.Name}: Deploy started for function {lambda.DeclaringType}::{lambda.Method} (C#), stage: {options.TargetStage}"
                );
                if (options.NotReally)
                {
                    Console.WriteLine($"{lambda.Name}: (Not really) Updating lambda function...");
                    DeployInternal(lambda, options.TargetStage, options);
                    Console.WriteLine($"{lambda.Name}: (Not really) Updating/creating alias...");
                    return;
                }
                Console.WriteLine($"{lambda.Name}: Updating lambda function...");
                DeployInternal(lambda, options.TargetStage, options);
                if (!lambda.IsNewFunction)
                {
                    Deployer.SetStage(lambda, options);
                }
            }
            catch (Exception ex)
            {
                // Add lambda name to exception message
                throw new Exception($"Error deploying C# lambda '{lambda?.Name ?? "unknown"}': {ex.Message}", ex);
            }
        }

        private static void DeployInternal(LambdaSettings lambda, EnvironmentStage stage, Options options)
        {
            try
            {
                if (lambda == null)
                {
                    throw new ArgumentNullException(nameof(lambda), "Lambda settings cannot be null");
                }

                var folderPath = BaseDeployer.GetDirectoryPath(options, lambda);
                var defaultsPath = BaseDeployer.GetDefaultsPath(options, lambda);
                var configPath = BaseDeployer.GetConfigPath(options, lambda);

                var currentDir = Directory.GetCurrentDirectory();
                var process = new Process();
                var configProcess = new Process();

                // Create platform-specific process start info
                bool isWindows = RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
                var startInfo = new ProcessStartInfo();
                var configStartInfo = new ProcessStartInfo();

                if (isWindows)
                {
                    startInfo.FileName = "cmd";
                    configStartInfo.FileName = "cmd";
                }
                else
                {
                    startInfo.FileName = "/bin/bash";
                    configStartInfo.FileName = "/bin/bash";
                }

                startInfo.RedirectStandardOutput = true;
                configStartInfo.RedirectStandardOutput = true;
                if (!options.Verbose)
                {
                    startInfo.RedirectStandardError = true;
                    startInfo.UseShellExecute = false;
                    configStartInfo.RedirectStandardError = true;
                    configStartInfo.UseShellExecute = false;
                }

                var dotnetCommand = new StringBuilder("dotnet lambda deploy-function");
                var configCommand = new StringBuilder(
                    "dotnet lambda update-function-config --apply-defaults true --config-file \"" + configPath + "\""
                );
                dotnetCommand.Append(" -fn " + lambda.Name);
                configCommand.Append(" -fn " + lambda.Name);
                var mn = lambda.AssemblyName + "::" + lambda.DeclaringType + "::" + lambda.Method;
                dotnetCommand.Append(" -fh " + mn);

                BaseDeployer.PrepareConfig(lambda, defaultsPath, configPath, options);

                System.IO.Directory.SetCurrentDirectory(folderPath);

                if (options.NotReally)
                {
                    System.IO.Directory.SetCurrentDirectory(currentDir);
                    return;
                }

                try
                {
                    ReportHelper.StartAction(lambda.Name, "C#", "$LATEST");
                }
                catch (Exception e)
                {
                    throw new Exception($"{lambda.Name}: ReportHelper StartAction failure: {e}");
                }
                try
                {
                    DeployLambdaFunction(
                        dotnetCommand,
                        lambda,
                        startInfo,
                        configStartInfo,
                        configProcess,
                        process,
                        configCommand,
                        options,
                        currentDir,
                        isWindows
                    );
                }
                catch (Exception e)
                {
                    ReportHelper.FailAction(e.Message, "");
                    throw new Exception($"{lambda.Name}: Failed to deploy lambda function: {e.Message}", e);
                }
            }
            catch (Exception ex)
            {
                ReportHelper.FailAction(ex.Message, "");
                Console.WriteLine($"{lambda.Name}: Error deploying function: {ex.Message}");
                throw new Exception($"Error in DeployInternal for C# lambda '{lambda?.Name ?? "unknown"}': {ex.Message}", ex);
            }
        }

        private static void DeployLambdaFunction(
            StringBuilder dotnetCommand,
            LambdaSettings lambda,
            ProcessStartInfo startInfo,
            ProcessStartInfo configStartInfo,
            Process configProcess,
            Process process,
            StringBuilder configCommand,
            Options options,
            string currentDir,
            bool isWindows)
        {
            string error = "";
            string output = "";

            if (isWindows)
            {
                startInfo.Arguments = "/c " + dotnetCommand.ToString();
                configStartInfo.Arguments = "/c " + configCommand.ToString();
            }
            else
            {
                startInfo.Arguments = "-c \"" + dotnetCommand.ToString() + "\"";
                configStartInfo.Arguments = "-c \"" + configCommand.ToString() + "\"";
            }

            process.StartInfo = startInfo;
            configProcess.StartInfo = configStartInfo;

            Console.WriteLine($"{lambda.Name}: Executing command: {dotnetCommand}");
            process.Start();
            output = process.StandardOutput.ReadToEnd();
            error = process.StandardError.ReadToEnd();

            // Always log the output and error for debugging purposes
            Console.WriteLine($"{lambda.Name}: Command output: {output}");
            if (!string.IsNullOrWhiteSpace(error))
            {
                Console.WriteLine($"{lambda.Name}: Command error: {error}");
            }

            process.WaitForExit(60 * 1000 * 2);
            lambda.IsNewFunction = output.Contains("New Lambda function created");
            if (process.ExitCode != 0 || !string.IsNullOrWhiteSpace(error))
            {
                throw new Exception($"{lambda.Name}: Failed to deploy lambda function. Exit code: {process.ExitCode}, Error: {error}");
            }

            Console.WriteLine($"{lambda.Name}: Created/updated function, waiting for {options.InterimSleepMs}ms for propagation");
            Thread.Sleep(options.InterimSleepMs);
            Console.WriteLine($"{lambda.Name}: Updating function configuration");
            configProcess.Start();
            error = "";
            output = configProcess.StandardOutput.ReadToEnd();
            error = configProcess.StandardError.ReadToEnd();

            // Always log the output and error for debugging purposes
            Console.WriteLine($"{lambda.Name}: Config command output: {output}");
            if (!string.IsNullOrWhiteSpace(error))
            {
                Console.WriteLine($"{lambda.Name}: Config command error: {error}");
            }
        }
    }
}
