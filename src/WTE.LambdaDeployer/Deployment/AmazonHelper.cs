using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using Newtonsoft.Json.Linq;
using WTE.LambdaDeployer.Common.ConfigModel;

namespace WTE.LambdaDeployer.Deployment
{
    public static class AmazonHelper
    {
        private static bool IsWindows => RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
        private const int retryCount = 3;
        private const int delayMilliseconds = 1000;

        private static ProcessStartInfo CreatePlatformSpecificStartInfo()
        {
            var startInfo = new ProcessStartInfo();
            if (IsWindows)
            {
                startInfo.FileName = "cmd";
                startInfo.UseShellExecute = false;
                startInfo.RedirectStandardOutput = true;
                startInfo.RedirectStandardError = true;
            }
            else
            {
                startInfo.FileName = "/bin/bash";
                startInfo.UseShellExecute = false;
                startInfo.RedirectStandardOutput = true;
                startInfo.RedirectStandardError = true;
            }
            return startInfo;
        }

        private static string FormatArguments(string command)
        {
            if (IsWindows)
            {
                return $"/c {command}";
            }
            else
            {
                return $"-c \"{command}\"";
            }
        }

        private static T Retry<T>(Func<T> action)
        {
            for (int attempt = 0; attempt < retryCount; attempt++)
            {
                try
                {
                    return action();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Attempt {attempt + 1} failed: {ex.Message}");
                    if (attempt == retryCount - 1)
                        throw;
                    Thread.Sleep(delayMilliseconds);
                }
            }
            return default;
        }

        public static bool CheckAlias(LambdaSettings lambda, string alias, string profileName)
        {
            return Retry(() =>
            {
                Console.WriteLine($"{lambda.Name}: Check version for {alias} alias");
                var process = new Process();
                var startInfo = CreatePlatformSpecificStartInfo();

                var command = new StringBuilder("aws lambda get-alias");
                command.Append(" --function-name " + lambda.Name);
                command.Append(" --name " + alias);
                if (!string.IsNullOrWhiteSpace(profileName))
                {
                    command.Append(" --profile " + profileName);
                }
                command.Append(" --output json");

                startInfo.Arguments = FormatArguments(command.ToString());
                process.StartInfo = startInfo;
                process.Start();
                var error = process.StandardError.ReadToEnd();
                process.WaitForExit();

                return !error.Contains("Cannot find alias");
            });
        }

        public static string GetAliasVersion(LambdaSettings lambda, string alias, string profileName)
        {
            return Retry(() =>
            {
                Console.WriteLine($"{lambda.Name}: Getting version for {alias} alias");
                var process = new Process();
                var startInfo = CreatePlatformSpecificStartInfo();

                var command = new StringBuilder("aws lambda get-alias");
                command.Append(" --function-name " + lambda.Name);
                command.Append(" --name " + alias);
                if (!string.IsNullOrWhiteSpace(profileName))
                {
                    command.Append(" --profile " + profileName);
                }
                command.Append(" --output json");

                startInfo.Arguments = FormatArguments(command.ToString());
                process.StartInfo = startInfo;
                process.Start();
                var error = process.StandardError.ReadToEnd();
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();
                if (process.ExitCode == 0)
                {
                    dynamic json = JObject.Parse(output);
                    return json.FunctionVersion;
                }
                else
                {
                    return null;
                }
            });
        }

        public static bool CreateAlias(LambdaSettings lambda, string alias, string version, string profileName)
        {
            return Retry(() =>
            {
                Console.WriteLine($"{lambda.Name}: Creating {alias} alias with version {version}");
                var process = new Process();
                var startInfo = CreatePlatformSpecificStartInfo();

                var command = new StringBuilder("aws lambda create-alias");
                command.Append(" --function-name " + lambda.Name);
                command.Append(" --name " + alias);
                command.Append(" --function-version " + version);
                if (!string.IsNullOrWhiteSpace(profileName))
                {
                    command.Append(" --profile " + profileName);
                }
                command.Append(" --output json");

                startInfo.Arguments = FormatArguments(command.ToString());
                process.StartInfo = startInfo;
                process.Start();
                var output = process.StandardOutput.ReadToEnd();
                var error = process.StandardError.ReadToEnd();
                process.WaitForExit();
                if (!string.IsNullOrEmpty(error))
                {
                    Console.WriteLine($"{lambda.Name}: Error occurred while creating alias: " + error);
                }
                return string.IsNullOrEmpty(error);
            });
        }

        public static string PublishVersion(LambdaSettings lambda, string profileName)
        {
            return Retry(() =>
            {
                Console.WriteLine($"{lambda.Name}: Publishing new version");
                var process = new Process();
                var startInfo = CreatePlatformSpecificStartInfo();

                var command = new StringBuilder("aws lambda publish-version");
                command.Append(" --function-name " + lambda.Name);
                if (!string.IsNullOrWhiteSpace(profileName))
                {
                    command.Append(" --profile " + profileName);
                }
                command.Append(" --output json");

                startInfo.Arguments = FormatArguments(command.ToString());
                process.StartInfo = startInfo;
                process.Start();
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();
                var error = process.StandardError.ReadToEnd();
                if (!string.IsNullOrEmpty(error))
                {
                    throw new Exception("Error occurred while publishing version: " + error);
                }
                else
                {
                    dynamic json = JObject.Parse(output);
                    Console.WriteLine($"{lambda.Name}: Published version: {json.Version}");
                    return json.Version;
                }
            });
        }

        public static bool UpdateAlias(LambdaSettings lambda, string alias, string version, string profileName)
        {
            return Retry(() =>
            {
                Console.WriteLine($"{lambda.Name}: Updating {alias} alias to version {version}");
                var process = new Process();
                var startInfo = CreatePlatformSpecificStartInfo();

                var command = new StringBuilder("aws lambda update-alias");
                command.Append(" --function-name " + lambda.Name);
                command.Append(" --name " + alias);
                command.Append(" --function-version " + version);
                if (!string.IsNullOrWhiteSpace(profileName))
                {
                    command.Append(" --profile " + profileName);
                }
                command.Append(" --output json");

                startInfo.Arguments = FormatArguments(command.ToString());
                process.StartInfo = startInfo;
                process.Start();
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();
                var error = process.StandardError.ReadToEnd();
                if (!string.IsNullOrEmpty(error))
                {
                    throw new Exception("Error occurred while updating alias: " + error);
                }
                return string.IsNullOrEmpty(error);
            });
        }

        public static bool FunctionExists(LambdaSettings lambda, string profileName)
        {
            return Retry(() =>
            {
                Console.WriteLine($"{lambda.Name}: Checking if function exists");
                var process = new Process();
                var startInfo = CreatePlatformSpecificStartInfo();

                var command = new StringBuilder("aws lambda get-function");
                command.Append(" --function-name " + lambda.Name);
                if (!string.IsNullOrWhiteSpace(profileName))
                {
                    command.Append(" --profile " + profileName);
                }

                startInfo.Arguments = FormatArguments(command.ToString());
                process.StartInfo = startInfo;

                var output = new StringBuilder();
                var error = new StringBuilder();

                try
                {
                    using (AutoResetEvent outputWaitHandle = new AutoResetEvent(false))
                    using (AutoResetEvent errorWaitHandle = new AutoResetEvent(false))
                    {
                        process.OutputDataReceived += (sender, e) =>
                        {
                            if (e.Data == null)
                            {
                                outputWaitHandle.Set();
                            }
                            else
                            {
                                output.AppendLine(e.Data);
                            }
                        };
                        process.ErrorDataReceived += (sender, e) =>
                        {
                            if (e.Data == null)
                            {
                                errorWaitHandle.Set();
                            }
                            else
                            {
                                error.AppendLine(e.Data);
                            }
                        };

                        process.Start();

                        process.BeginOutputReadLine();
                        process.BeginErrorReadLine();

                        if (process.WaitForExit(30000) && outputWaitHandle.WaitOne(30000) && errorWaitHandle.WaitOne(30000))
                        {
                            return !error.ToString().Contains("Function not found");
                        }
                        else
                        {
                            throw new Exception("Function existence check timed out");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"{lambda.Name}: Error checking if function exists: {ex.Message}");
                    return false;
                }
            });
        }

        public static bool CreateFunction(LambdaSettings lambda, string zipLocation, string configPath, string profileName)
        {
            return Retry(() =>
            {
                Console.WriteLine($"{lambda.Name}: Creating function");
                var process = new Process();
                var startInfo = CreatePlatformSpecificStartInfo();

                var command = new StringBuilder("aws lambda create-function");
                command.Append(" --function-name " + lambda.Name);
                if (!string.IsNullOrWhiteSpace(profileName))
                {
                    command.Append(" --profile " + profileName);
                }
                command.Append(" --output json");
                command.Append(" --zip-file \"fileb://" + zipLocation + "\"");
                command.Append(" --cli-input-json \"file://" + configPath + "\"");

                startInfo.Arguments = FormatArguments(command.ToString());
                process.StartInfo = startInfo;
                process.Start();
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();

                var error = process.StandardError.ReadToEnd();
                if (!string.IsNullOrEmpty(error))
                {
                    throw new Exception("Error occurred while creating function: " + error);
                }
                lambda.IsNewFunction = true;
                return string.IsNullOrEmpty(error);
            });
        }

        public static bool UpdateFunctionCode(LambdaSettings lambda, string zipLocation, string profileName)
        {
            return Retry(() =>
            {
                Console.WriteLine($"{lambda.Name}: Updating function code");
                var process = new Process();
                var startInfo = CreatePlatformSpecificStartInfo();

                var command = new StringBuilder("aws lambda update-function-code");
                command.Append(" --function-name " + lambda.Name);
                if (!string.IsNullOrWhiteSpace(profileName))
                {
                    command.Append(" --profile " + profileName);
                }
                command.Append(" --zip-file \"fileb://" + zipLocation + "\"");

                startInfo.Arguments = FormatArguments(command.ToString());
                process.StartInfo = startInfo;
                process.Start();
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();

                var error = process.StandardError.ReadToEnd();
                if (!string.IsNullOrEmpty(error))
                {
                    throw new Exception("Error occurred while updating function code: " + error);
                }
                return string.IsNullOrEmpty(error);
            });
        }

        public static bool UpdateFunctionConfiguration(LambdaSettings lambda, string configPath, string profileName)
        {
            return Retry(() =>
            {
                Console.WriteLine($"{lambda.Name}: Updating function configuration");
                var process = new Process();
                var startInfo = CreatePlatformSpecificStartInfo();

                var command = new StringBuilder("aws lambda update-function-configuration");
                command.Append(" --function-name " + lambda.Name);
                if (!string.IsNullOrWhiteSpace(profileName))
                {
                    command.Append(" --profile " + profileName);
                }
                command.Append(" --output json");
                command.Append($" --cli-input-json \"file://{configPath}\"");

                startInfo.Arguments = FormatArguments(command.ToString());
                process.StartInfo = startInfo;
                process.Start();
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();

                var error = process.StandardError.ReadToEnd();
                if (!string.IsNullOrEmpty(error))
                {
                    throw new Exception("Error occurred while updating function configuration: " + error);
                }
                return string.IsNullOrEmpty(error);
            });
        }

        public static string CmdEscape(this string x)
        {
            if (IsWindows)
            {
                return x.Replace(" ", "^ ");
            }
            return x.Replace(" ", "\\ ");
        }
    }
}
