using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using Amazon.SimpleNotificationService.Model;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using WTE.LambdaDeployer.Common.ConfigModel;
using WTE.LambdaDeployer.Common.Enums;
using WTE.LambdaDeployer.Helpers;
using WTE.LambdaDeployer.Reporting;

namespace WTE.LambdaDeployer.Deployment
{
    public static class Deployer
    {
        public static void ScanAlias(Options options)
        {
            LogLine(options, "Called scan alias for options:\n" + options.ToString());

            ReportHelper.StartDeployment(false);

            List<LambdaSettings> lambdas;
            var hasTarget = options.HasTargetStage();
            var targetStage = options.GetTargetStage();
            foreach (var language in (LambdaLanguage[])Enum.GetValues(typeof(LambdaLanguage)))
            {
                LogLine(options, "Scanning {0} functions", language.ToString());
                lambdas = ConfigHelper.GetAllOfLanguage(options, language).Lambdas;

                foreach (var ln in lambdas)
                {
                    if (hasTarget)
                    {
                        LogLine(options, "Checking alias {0}:{1}", ln.Name, targetStage);
                        var ver = AmazonHelper.GetAliasVersion(ln, targetStage, options.AWSProfileName) ?? "N/A";
                        ReportHelper.LogFullAction(
                            new ActionEntry()
                            {
                                Alias = targetStage,
                                FunctionName = ln.Name,
                                OldVersion = ver,
                                NewVersion = ver,
                                Language = language.ToString(),
                                Success = true,
                                ModifiedAt = DateTime.UtcNow,
                            }
                        );
                    }
                    else
                    {
                        LogLine(options, "Found function {0}", ln.Name);
                        ReportHelper.LogFullAction(
                            new ActionEntry()
                            {
                                Alias = "$LATEST",
                                FunctionName = ln.Name,
                                OldVersion = "$LATEST",
                                NewVersion = "$LATEST",
                                Language = language.ToString(),
                                Success = true,
                                ModifiedAt = DateTime.UtcNow,
                            }
                        );
                    }
                }
            }

            ReportHelper.FinishDeployment();
            if (!Directory.Exists("./ResultReport/"))
            {
                Directory.CreateDirectory("./ResultReport/");
            }

            // Use FileShare.ReadWrite when writing the file
            using (var fs = new FileStream("./ResultReport/index.html", FileMode.Create, FileAccess.Write, FileShare.Read))
            {
                using (var sw = new StreamWriter(fs))
                {
                    sw.Write(ReportHelper.GetPrint());
                }
            }
        }

        public static void Deploy(Options options, LambdaConfig preloadedConfig = null)
        {
            LogLine(options, "Called deploy for options:" + options.ToString());
            ReportHelper.StartDeployment(options.PromoteStage);

            var config = preloadedConfig ?? ConfigHelper.GetConfig(options);
            Console.WriteLine("Checking config " + string.Join(", ", config.Lambdas));

            // Detect languages for all lambdas
            foreach (var lambda in options.LambdasToDeploy)
            {
                if (lambda.Language == LambdaLanguage.Detect)
                {
                    var ld = config.Lambdas.FirstOrDefault(l => l.Name == lambda.LambdaName);
                    if (ld == null)
                    {
                        throw new Exception($"Lambda {lambda.LambdaName} not found in config.");
                    }

                    var group = config.Groups.FirstOrDefault(g => g.GroupName == ld.GroupName);
                    if (group == null)
                    {
                        throw new Exception($"No group config found for group {ld.GroupName} (lambda {lambda.LambdaName})");
                    }

                    lambda.Language = group.Language;
                }
            }

            // Now proceed with deployment
            if (options.DeployAll)
            {
                foreach (var lang in (LambdaLanguage[])Enum.GetValues(typeof(LambdaLanguage)))
                {
                    DeployAllLanguage(lang, options, config);
                }
            }
            else if (options.DeployAllLanguage.Count > 0)
            {
                foreach (var dal in options.DeployAllLanguage)
                {
                    DeployAllLanguage(dal, options, config);
                }
            }
            else
            {
                // Group lambdas by language for parallel deployment
                var lambdasByLanguage = options.LambdasToDeploy
                    .GroupBy(l => l.Language)
                    .ToDictionary(g => g.Key, g => g.ToList());

                foreach (var langGroup in lambdasByLanguage)
                {
                    DeployLanguage(langGroup.Key, options, config, langGroup.Value);
                }
            }

            ReportHelper.FinishDeployment();
            if (!Directory.Exists("./ResultReport/"))
            {
                Directory.CreateDirectory("./ResultReport/");
            }

            // Use FileShare.ReadWrite when writing the file
            using (var fs = new FileStream("./ResultReport/index.html", FileMode.Create, FileAccess.Write, FileShare.Read))
            {
                using (var sw = new StreamWriter(fs))
                {
                    sw.Write(ReportHelper.GetPrint());
                }
            }
        }

        public static void DeployAllLanguage(LambdaLanguage language, Options options, LambdaConfig config = null)
        {
            if (options.PromoteStage)
            {
                var lambdas = config != null
                    ? config.Lambdas.Where(l => config.Groups.Any(g => g.GroupName == l.GroupName && g.Language == language)).ToList()
                    : ConfigHelper.GetAllOfLanguage(options, language).Lambdas;
                PromoteStage(lambdas, options);
            }
            else
            {
                switch (language)
                {
                    case LambdaLanguage.CSharp:
                        CSharpDeployer.DeployAll(options, config);
                        break;
                    case LambdaLanguage.Nodejs:
                        NodejsDeployer.DeployAll(options, config);
                        break;
                    case LambdaLanguage.Python:
                        PythonDeployer.DeployAll(options, config);
                        break;
                }
            }
        }

        public static void DeployLanguage(LambdaLanguage language, Options options, LambdaConfig config = null, List<LambdaDefinition> specificLambdas = null)
        {
            if (specificLambdas == null)
            {
                specificLambdas = options.LambdasToDeploy.Where(ltd => ltd.Language == language).ToList();
            }

            if (specificLambdas.Any())
            {
                switch (language)
                {
                    case LambdaLanguage.CSharp:
                        if (options.PromoteStage)
                        {
                            var lambdas = CSharpDeployer.GetLambdaSettings(options, config);
                            PromoteStage(lambdas, options);
                        }
                        else
                        {
                            CSharpDeployer.Deploy(options, config, specificLambdas);
                        }
                        break;
                    case LambdaLanguage.Nodejs:
                        if (options.PromoteStage)
                        {
                            var lambdas = NodejsDeployer.GetLambdaSettings(options, config);
                            PromoteStage(lambdas, options);
                        }
                        else
                        {
                            NodejsDeployer.Deploy(options, config, specificLambdas);
                        }
                        break;
                    case LambdaLanguage.Python:
                        if (options.PromoteStage)
                        {
                            var lambdas = PythonDeployer.GetLambdaSettings(options, config);
                            PromoteStage(lambdas, options);
                        }
                        else
                        {
                            PythonDeployer.Deploy(options, config, specificLambdas);
                        }
                        break;
                }
            }
        }

        private static void Log(Options options, string format, params string[] vars)
        {
            if (options.Verbose)
            {
                Console.WriteLine(format, vars);
            }
        }

        private static void LogLine(Options options, string format, params string[] vars)
        {
            if (options.Verbose)
            {
                Console.WriteLine(format, vars);
            }
        }

        private static void PromoteStage(List<LambdaSettings> lambdas, Options options)
        {
            foreach (var lambda in lambdas)
            {
                if (options.NotReally)
                {
                    var targetStage = options.GetTargetStage();
                    var sourceStage = options.GetSourceStage();
                    LogLine(
                        options,
                        "(Not really) Promoting stage " + targetStage + " to current " + sourceStage + " for lambda " + lambda.Name
                    );
                }
                else if (options.HasTargetStage())
                {
                    var targetStage = options.GetTargetStage();
                    var sourceStage = options.GetSourceStage();
                    LogLine(options, "Promoting stage " + targetStage + " to the value of " + sourceStage);
                    if (options.BackupRequired())
                    {
                        var rollbackStage = options.GetBackupStage();
                        if (rollbackStage != targetStage)
                        {
                            LogLine(options, "Setting up " + rollbackStage);
                            PromoteAlias(lambda, rollbackStage, targetStage, options.AWSProfileName);
                        }
                    }
                    PromoteAlias(lambda, targetStage, sourceStage, options.AWSProfileName);
                }
            }
        }

        public static void SetStage(LambdaSettings lambda, Options options)
        {
            Log(options, "Publishing new version...");
            var newVersion = AmazonHelper.PublishVersion(lambda, options.AWSProfileName);
            LogLine(options, "done.");
            var action = new ActionEntry() { FunctionName = lambda.Name };
            if (string.IsNullOrEmpty(newVersion))
            {
                Console.WriteLine("Error publishing the function " + lambda.Name + ", exiting");
                action.Success = false;
                action.ModifiedAt = DateTime.UtcNow;
                action.ErrorMessage = "Failed to retrieve new version";
                ReportHelper.LogFullAction(action);
                throw new Exception("Function version absent.");
            }

            int oldVersion;
            if (int.TryParse(newVersion, out oldVersion))
            {
                oldVersion--;
                try
                {
                    ReportHelper.SetOldVersion(lambda.Name, "C#", "$LATEST", oldVersion.ToString());
                }
                catch (Exception e)
                {
                    Console.WriteLine("Set oldVersion error: " + e.ToString());
                }
            }

            ReportHelper.UpdateToNewVersion(lambda.Name, "C#", "$LATEST", oldVersion.ToString(), newVersion);

            action.NewVersion = newVersion;

            if (options.ShouldDeployTarget())
            {
                var targetStage = options.GetTargetStage();
                action.Alias = targetStage;

                if (options.BackupRequired())
                {
                    var rollbackStage = options.GetBackupStage();
                    if (rollbackStage != targetStage)
                    {
                        LogLine(options, "Setting up rollback stage " + rollbackStage);
                        PromoteAlias(lambda, rollbackStage, targetStage, options.AWSProfileName);
                    }
                }
                var oldStageVersion = AmazonHelper.GetAliasVersion(lambda, targetStage, options.AWSProfileName);
                action.OldVersion = oldStageVersion;
                LogLine(
                    options,
                    "Setting alias " + targetStage + " (" + oldStageVersion + ") to the newly published version (" + newVersion + ")"
                );
                action.Success = PublishAlias(lambda, newVersion, targetStage, options.AWSProfileName);
                action.ModifiedAt = DateTime.UtcNow;
                ReportHelper.LogFullAction(action);
            }
        }

        public static void PromoteAlias(LambdaSettings lambda, string target, string source, string profileName)
        {
            Console.WriteLine($"{lambda.Name}: Checking function version for {source} alias");
            var version = AmazonHelper.GetAliasVersion(lambda, source, profileName);
            var currentVersion = AmazonHelper.GetAliasVersion(lambda, target, profileName);
            var action = new ActionEntry()
            {
                FunctionName = lambda.Name,
                Alias = target,
                OldVersion = currentVersion,
                NewVersion = version,
            };
            if (version == null)
            {
                Console.WriteLine($"{lambda.Name}: Alias doesn't exist or an error occurred. Skipping");
                action.Success = false;
                action.ErrorMessage = "Source stage doesn't exist or an error occurred retrieving it.";
                action.ModifiedAt = DateTime.UtcNow;
                ReportHelper.LogFullAction(action);
            }
            else
            {
                Console.WriteLine($"{lambda.Name}: Updating {target} alias from version {currentVersion} to {version}");
                action.Success = PublishAlias(lambda, version, target, profileName);
                if (!action.Success)
                {
                    action.ErrorMessage = "Failed to update alias";
                }
                action.ModifiedAt = DateTime.UtcNow;
                ReportHelper.LogFullAction(action);
            }
        }

        public static bool PublishAlias(LambdaSettings lambda, string version, string stage, string profileName)
        {
            var success = false;

            Console.WriteLine($"{lambda.Name}: Checking if {stage} alias exists");
            if (AmazonHelper.CheckAlias(lambda, stage.ToString(), profileName))
            {
                Console.WriteLine($"{lambda.Name}: Updating {stage} alias");
                success = AmazonHelper.UpdateAlias(lambda, stage, version, profileName);
            }
            else
            {
                Console.WriteLine($"{lambda.Name}: Creating {stage} alias");
                success = AmazonHelper.CreateAlias(lambda, stage, version, profileName);
            }

            return success;
        }
    }
}
