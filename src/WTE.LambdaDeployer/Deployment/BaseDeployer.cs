using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using WTE.LambdaDeployer.Common.ConfigModel;
using WTE.LambdaDeployer.Common.Enums;
using WTE.LambdaDeployer.Helpers;
using WTE.LambdaDeployer.Reporting;

namespace WTE.LambdaDeployer.Deployment
{
    public static class BaseDeployer
    {
        private static readonly ConcurrentDictionary<string, string> defaultsCache = new ConcurrentDictionary<string, string>();
        private static readonly ConcurrentDictionary<string, string> configCache = new ConcurrentDictionary<string, string>();
        private static readonly SemaphoreSlim fileAccessSemaphore = new SemaphoreSlim(1, 1);

        public static void Log(Options options, string format, params string[] vars)
        {
            if (options.Verbose)
            {
                Console.WriteLine(format, vars);
            }
        }

        public static void LogLine(Options options, string format, params string[] vars)
        {
            if (options.Verbose)
            {
                Console.WriteLine(format, vars);
            }
        }

        public static string GetDirectoryPath(Options options, LambdaSettings lambda)
        {
            try
            {
                if (lambda == null)
                {
                    throw new ArgumentNullException(nameof(lambda), "Lambda settings cannot be null");
                }

                var languageGroupConfig = ConfigHelper.GetLanguageGroupConfig(options, lambda);
                if (languageGroupConfig == null)
                {
                    throw new InvalidOperationException($"Could not find language group configuration for lambda '{lambda.Name}' with group '{lambda.GroupName}'");
                }

                string directoryPath;
                if (Path.IsPathRooted(languageGroupConfig.Directory))
                {
                    directoryPath = languageGroupConfig.Directory;
                }
                else
                {
                    // Use Path.GetFullPath to resolve any relative path components
                    directoryPath = Path.GetFullPath(Path.Combine(options.LambdaDirectory, languageGroupConfig.Directory));
                }

                Console.WriteLine($"{lambda.Name}: Directory path: {directoryPath}");
                return directoryPath;
            }
            catch (Exception ex) when (!(ex is ArgumentNullException) && !(ex is InvalidOperationException))
            {
                // Wrap other exceptions with more context
                throw new Exception($"Error getting directory path for lambda '{lambda?.Name ?? "unknown"}': {ex.Message}", ex);
            }
        }

        public static string GetDefaultsPath(Options options, LambdaSettings lambda)
        {
            try
            {
                if (lambda == null)
                {
                    throw new ArgumentNullException(nameof(lambda), "Lambda settings cannot be null");
                }

                // Try to find the defaults file in multiple locations
                string defaultsPath = FindDefaultsFile(options, lambda).Replace('\\', '/');
                Console.WriteLine($"{lambda.Name}: Defaults path: {defaultsPath}");
                return defaultsPath;
            }
            catch (Exception ex) when (!(ex is ArgumentNullException) && !(ex is InvalidOperationException))
            {
                // Wrap other exceptions with more context
                throw new Exception($"Error getting defaults path for lambda '{lambda?.Name ?? "unknown"}': {ex.Message}", ex);
            }
        }

        public static string GetConfigPath(Options options, LambdaSettings lambda)
        {
            try
            {
                if (lambda == null)
                {
                    throw new ArgumentNullException(nameof(lambda), "Lambda settings cannot be null");
                }

                var languageGroupConfig = ConfigHelper.GetLanguageGroupConfig(options, lambda);
                if (languageGroupConfig == null)
                {
                    throw new InvalidOperationException($"Could not find language group configuration for lambda '{lambda.Name}' with group '{lambda.GroupName}'");
                }

                string configPath;
                if (Path.IsPathRooted(languageGroupConfig.ConfigPath))
                {
                    configPath = languageGroupConfig.ConfigPath;
                }
                else
                {
                    // Normalize the path to avoid duplications
                    // Use Path.GetFullPath to resolve any relative path components
                    configPath = Path.GetFullPath(Path.Combine(options.LambdaDirectory, languageGroupConfig.ConfigPath));
                }

                Console.WriteLine($"{lambda.Name}: Config path: {configPath}");
                return configPath.Replace('\\', '/');
            }
            catch (Exception ex) when (!(ex is ArgumentNullException) && !(ex is InvalidOperationException))
            {
                // Wrap other exceptions with more context
                throw new Exception($"Error getting config path for lambda '{lambda?.Name ?? "unknown"}': {ex.Message}", ex);
            }
        }

        public static List<LambdaSettings> GetLambdaSettings(Options options, LambdaLanguage language, string languageName, LambdaConfig config = null)
        {
            config = config ?? ConfigHelper.GetConfig(options);
            var lambdas = config
                .Lambdas.Where(l => options.LambdasToDeploy.Any(ld => ld.Language == language && ld.LambdaName == l.Name))
                .ToList();

            if (lambdas.Count == 0)
            {
                Console.WriteLine($"No {languageName} lambdas found to deploy");
            }

            return lambdas;
        }

        public static void DeployAll(Options options, LambdaLanguage language, string languageName, Action<LambdaSettings, Options> deploySingleFunc, LambdaConfig config = null)
        {
            try
            {
                config = config ?? ConfigHelper.GetAllOfLanguage(options, language);
                if (config == null || config.Lambdas == null)
                {
                    throw new InvalidOperationException($"Could not retrieve configuration for {languageName} lambdas");
                }

                // Filter lambdas by language
                var lambdasToProcess = config.Lambdas.Where(l =>
                    config.Groups.Any(g => g.GroupName == l.GroupName &&
                                          string.Equals(g.Language.ToString(), language.ToString(), StringComparison.OrdinalIgnoreCase)))
                    .ToList();

                // Validate required files for all lambdas before starting deployment
                var validationErrors = new ConcurrentBag<Exception>();
                Parallel.ForEach(
                    lambdasToProcess,
                    lambda =>
                    {
                        try
                        {
                            ValidateRequiredFiles(options, lambda);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"ERROR: {ex.Message}");
                        }
                    }
                );

                if (validationErrors.Count > 0)
                {
                    throw new AggregateException($"Failed to validate {validationErrors.Count} lambdas", validationErrors);
                }

                // Set max degree of parallelism
                int maxParallelism = options.MaxParallelDeployments > 0 ? options.MaxParallelDeployments : 5;

                Console.WriteLine($"Deploying {lambdasToProcess.Count} {languageName} functions with parallelism of {maxParallelism}");

                // Use a ConcurrentBag to collect exceptions with lambda context
                var exceptions = new ConcurrentBag<Exception>();

                Parallel.ForEach(
                    lambdasToProcess,
                    new ParallelOptions { MaxDegreeOfParallelism = maxParallelism },
                    lambda =>
                    {
                        try
                        {
                            deploySingleFunc(lambda, options);
                        }
                        catch (Exception ex)
                        {
                            // Add lambda name to exception message
                            var wrappedException = new Exception($"Error deploying lambda '{lambda.Name}': {ex.Message}", ex);
                            Console.WriteLine($"ERROR: {wrappedException.Message}");
                        }
                    }
                );

                // If we collected any exceptions, throw an AggregateException with all of them
                if (exceptions.Count > 0)
                {
                    throw new AggregateException($"Failed to deploy {exceptions.Count} lambdas", exceptions);
                }
            }
            catch (Exception ex) when (!(ex is AggregateException))
            {
                throw new Exception($"Error deploying {languageName} lambdas: {ex.Message}", ex);
            }
        }

        public static void Deploy(Options options, LambdaLanguage language, string languageName, Action<LambdaSettings, Options> deploySingleFunc,
            LambdaConfig config = null, List<LambdaDefinition> specificLambdas = null)
        {
            try
            {
                config = config ?? ConfigHelper.GetConfig(options);
                if (config == null)
                {
                    throw new InvalidOperationException("Could not retrieve lambda configuration");
                }

                var lambdasToDeploy = specificLambdas ?? options.LambdasToDeploy
                    .Where(ltd => ltd.Language == language)
                    .ToList();

                // Get the lambda settings for all lambdas to deploy
                var lambdaSettings = new List<LambdaSettings>();
                foreach (var deployLambda in lambdasToDeploy)
                {
                    var lambda = config.Lambdas.FirstOrDefault(l => l.Name == deployLambda.LambdaName);
                    if (lambda != null)
                    {
                        lambdaSettings.Add(lambda);
                    }
                }

                // Set max degree of parallelism
                int maxParallelism = options.MaxParallelDeployments > 0 ? options.MaxParallelDeployments : 5;

                Console.WriteLine($"Deploying {lambdasToDeploy.Count} {languageName} functions with parallelism of {maxParallelism}");

                var deployedLambdaSettings = new ConcurrentBag<LambdaSettings>();
                var exceptions = new ConcurrentBag<Exception>();

                Parallel.ForEach(
                    lambdasToDeploy,
                    new ParallelOptions { MaxDegreeOfParallelism = maxParallelism },
                    deploylambda =>
                    {
                        try
                        {
                            var lambda = config.Lambdas.FirstOrDefault(l => l.Name == deploylambda.LambdaName);
                            if (lambda == null)
                            {
                                var error = $"No configuration for lambda '{deploylambda.LambdaName}' found";
                                ReportHelper.LogFullAction(
                                    new ActionEntry()
                                    {
                                        Language = languageName,
                                        FunctionName = deploylambda.LambdaName,
                                        Success = false,
                                        ErrorMessage = error,
                                        ModifiedAt = DateTime.UtcNow,
                                    }
                                );
                                exceptions.Add(new Exception(error));
                            }
                            else
                            {
                                deploySingleFunc(lambda, options);
                                deployedLambdaSettings.Add(lambda);
                            }
                        }
                        catch (Exception ex)
                        {
                            var error = $"Error deploying lambda '{deploylambda.LambdaName}': {ex.Message}";
                            Console.WriteLine($"ERROR: {error}");
                            exceptions.Add(new Exception(error, ex));
                        }
                    }
                );

                // If we collected any exceptions, throw an AggregateException with all of them
                if (exceptions.Count > 0)
                {
                    throw new AggregateException($"Failed to deploy {exceptions.Count} lambdas", exceptions);
                }
            }
            catch (Exception ex) when (!(ex is AggregateException))
            {
                throw new Exception($"Error deploying {languageName} lambdas: {ex.Message}", ex);
            }
        }

        public static void PrepareConfig(LambdaSettings lambda, string defaultsPath, string configPath, Options options)
        {
            try
            {
                if (lambda == null)
                {
                    throw new ArgumentNullException(nameof(lambda), "Lambda settings cannot be null");
                }

                if (string.IsNullOrEmpty(defaultsPath))
                {
                    throw new ArgumentException("Defaults path cannot be null or empty", nameof(defaultsPath));
                }

                if (string.IsNullOrEmpty(configPath))
                {
                    throw new ArgumentException("Config path cannot be null or empty", nameof(configPath));
                }

                string defaults;

                // Check if defaults are already cached
                if (defaultsCache.TryGetValue(defaultsPath, out defaults))
                {
                    Console.WriteLine($"{lambda.Name}: Using cached defaults");
                }
                else
                {
                    // Not in cache, try to load from file with retries
                    if (!RetryFileExists(defaultsPath))
                    {
                        if (options.NotReally)
                        {
                            Console.WriteLine($"{lambda.Name}: (Not really) Creating defaults file at {defaultsPath}");
                            defaults = "{}"; // Dummy content for not-really mode
                        }
                        else
                        {
                            // Try to create the defaults file
                            defaultsPath = CreateDefaultsFile(options, lambda);

                            // Now read the created file with retries
                            defaults = RetryReadFile(defaultsPath);
                        }
                    }
                    else
                    {
                        // Read the file with retries
                        defaults = RetryReadFile(defaultsPath);
                    }

                    // Cache the defaults for future use
                    defaultsCache[defaultsPath] = defaults;
                }

                if (RetryFileExists(configPath) && !options.NotReally)
                {
                    try
                    {
                        File.Delete(configPath);
                    }
                    catch (IOException ex)
                    {
                        Console.WriteLine($"{lambda.Name}: Warning: Could not delete existing config file: {ex.Message}");
                        // Generate a unique filename if we can't delete the existing file
                        configPath = Path.Combine(
                            Path.GetDirectoryName(configPath),
                            $"{Path.GetFileNameWithoutExtension(configPath)}-{Guid.NewGuid():N}{Path.GetExtension(configPath)}"
                        );
                    }
                }

                var stringList = new List<string>();

                dynamic djson = JObject.Parse(defaults);
                if (djson["environment-variables"] != null)
                {
                    stringList.Add(djson["environment-variables"].ToString());
                }

                if (options.AdditionalVariables.Any())
                {
                    stringList.AddRange(options.AdditionalVariables.Select(a => "\"" + a.Key + "\"=\"" + a.Value + "\""));
                }

                if (lambda.EnvironmentVariables.Any())
                {
                    stringList.AddRange(lambda.EnvironmentVariables.Select(a => "\"" + a.Key + "\"=\"" + a.Value + "\""));
                }

                if (!string.IsNullOrWhiteSpace(options.AWSProfileName))
                {
                    djson["profile"] = options.AWSProfileName;
                }

                if (lambda.Timeout.HasValue && lambda.Timeout > 0)
                {
                    djson["function-timeout"] = lambda.Timeout.Value;
                }
                else if (options.Timeout.HasValue)
                {
                    djson["function-timeout"] = options.Timeout.Value;
                }

                if (lambda.Memory.HasValue && lambda.Memory > 0)
                {
                    djson["function-memory-size"] = lambda.Memory.Value;
                }
                else if (options.Memory.HasValue)
                {
                    djson["function-memory-size"] = options.Memory.Value;
                }

                if (!string.IsNullOrWhiteSpace(lambda.Runtime))
                {
                    djson["function-runtime"] = lambda.Runtime;
                }

                if (!string.IsNullOrWhiteSpace(lambda.Framework))
                {
                    djson["framework"] = lambda.Framework;
                }

                djson["environment-variables"] = string.Join(";", stringList);

                if (!options.NotReally)
                {
                    // Use FileShare.ReadWrite when writing the file
                    using (var fs = new FileStream(configPath, FileMode.Create, FileAccess.Write, FileShare.Read))
                    {
                        using (var sw = new StreamWriter(fs))
                        {
                            sw.Write(JsonConvert.SerializeObject(djson));
                        }
                    }
                }
            }
            catch (Exception ex) when (!(ex is ArgumentNullException) &&
                                      !(ex is ArgumentException) &&
                                      !(ex is FileNotFoundException))
            {
                // Wrap other exceptions with more context
                throw new Exception($"Error preparing config for lambda '{lambda?.Name ?? "unknown"}': {ex.Message}", ex);
            }
        }

        public static void ClearDefaultsCache()
        {
            defaultsCache.Clear();
        }

        public static void ValidateRequiredFiles(Options options, LambdaSettings lambda)
        {
            try
            {
                if (lambda == null)
                {
                    throw new ArgumentNullException(nameof(lambda), "Lambda settings cannot be null");
                }

                var folderPath = GetDirectoryPath(options, lambda);
                var defaultsPath = GetDefaultsPath(options, lambda);
                var configPath = GetConfigPath(options, lambda);

                // Check if directory exists
                if (!Directory.Exists(folderPath))
                {
                    throw new DirectoryNotFoundException($"Lambda directory not found for '{lambda.Name}': {folderPath}");
                }

                // Check if defaults file exists
                if (!File.Exists(defaultsPath))
                {
                    throw new FileNotFoundException($"Defaults file not found for lambda '{lambda.Name}'", defaultsPath);
                }

                // Check if we can create the config file
                var configDir = Path.GetDirectoryName(configPath);
                if (!Directory.Exists(configDir))
                {
                    throw new DirectoryNotFoundException($"Config directory not found for lambda '{lambda.Name}': {configDir}");
                }

                // All required files exist
                return;
            }
            catch (Exception ex)
            {
                // Wrap the exception with more context
                throw new Exception($"Error validating required files for lambda '{lambda?.Name ?? "unknown"}': {ex.Message}", ex);
            }
        }

        public static string FindDefaultsFile(Options options, LambdaSettings lambda)
        {
            try
            {
                if (lambda == null)
                {
                    throw new ArgumentNullException(nameof(lambda), "Lambda settings cannot be null");
                }

                var languageGroupConfig = ConfigHelper.GetLanguageGroupConfig(options, lambda);
                if (languageGroupConfig == null)
                {
                    throw new InvalidOperationException($"Could not find language group configuration for lambda '{lambda.Name}' with group '{lambda.GroupName}'");
                }

                // List of possible locations for the defaults file
                var possibleLocations = new List<string>();

                // 1. Try the path from the group config
                if (!string.IsNullOrEmpty(languageGroupConfig.DefaultsPath))
                {
                    if (Path.IsPathRooted(languageGroupConfig.DefaultsPath))
                    {
                        possibleLocations.Add(languageGroupConfig.DefaultsPath);
                    }
                    else
                    {
                        // Use Path.GetFullPath to resolve any relative path components
                        possibleLocations.Add(Path.GetFullPath(Path.Combine(options.LambdaDirectory, languageGroupConfig.DefaultsPath)));
                    }
                }

                // 2. Try in the lambda directory
                var lambdaDir = GetDirectoryPath(options, lambda);
                possibleLocations.Add(Path.Combine(lambdaDir, "autodeploy-defaults.json"));

                // 3. Try in the parent directory of the lambda directory
                var parentDir = Directory.GetParent(lambdaDir)?.FullName;
                if (!string.IsNullOrEmpty(parentDir))
                {
                    possibleLocations.Add(Path.Combine(parentDir, "autodeploy-defaults.json"));
                }

                // 4. Try in the src directory
                var srcDir = Path.Combine(options.LambdaDirectory, "src");
                if (Directory.Exists(srcDir))
                {
                    possibleLocations.Add(Path.Combine(srcDir, "autodeploy-defaults.json"));

                    // 5. Try in language-specific directories under src
                    var csharpDir = Path.Combine(srcDir, "csharp");
                    if (Directory.Exists(csharpDir))
                    {
                        possibleLocations.Add(Path.Combine(csharpDir, "autodeploy-defaults.json"));

                        // 6. Try in project-specific directories under src/csharp
                        foreach (var dir in Directory.GetDirectories(csharpDir))
                        {
                            possibleLocations.Add(Path.Combine(dir, "autodeploy-defaults.json"));
                        }
                    }
                }

                // 7. Try in the root directory
                possibleLocations.Add(Path.Combine(options.LambdaDirectory, "autodeploy-defaults.json"));

                // 8. Try relative to the current directory
                possibleLocations.Add("autodeploy-defaults.json");
                possibleLocations.Add("../autodeploy-defaults.json");

                // Remove any duplicate paths
                possibleLocations = possibleLocations.Distinct().ToList();

                // Check each location with retries
                foreach (var location in possibleLocations)
                {
                    bool exists = RetryFileExists(location, 3, 500);
                    if (exists)
                    {
                        Console.WriteLine($"{lambda.Name}: Found defaults file at: {location}");
                        return location;
                    }
                }

                // If we get here and options.CreateMissingDefaults is true, create the defaults file
                if (options.CreateMissingDefaults)
                {
                    Console.WriteLine($"{lambda.Name}: Creating missing defaults file");
                    return CreateDefaultsFile(options, lambda);
                }

                // If we get here, we couldn't find the defaults file
                Console.WriteLine($"{lambda.Name}: ERROR: Could not find defaults file in any of these locations:");
                foreach (var location in possibleLocations)
                {
                    Console.WriteLine($"{lambda.Name}:   - {location}");
                }

                throw new FileNotFoundException($"Defaults file not found for lambda '{lambda.Name}'", possibleLocations.FirstOrDefault() ?? "unknown");
            }
            catch (Exception ex) when (!(ex is ArgumentNullException) && !(ex is InvalidOperationException) && !(ex is FileNotFoundException))
            {
                // Wrap other exceptions with more context
                throw new Exception($"Error finding defaults file for lambda '{lambda?.Name ?? "unknown"}': {ex.Message}", ex);
            }
        }

        public static string CreateDefaultsFile(Options options, LambdaSettings lambda)
        {
            try
            {
                if (lambda == null)
                {
                    throw new ArgumentNullException(nameof(lambda), "Lambda settings cannot be null");
                }

                var languageGroupConfig = ConfigHelper.GetLanguageGroupConfig(options, lambda);
                if (languageGroupConfig == null)
                {
                    throw new InvalidOperationException($"Could not find language group configuration for lambda '{lambda.Name}' with group '{lambda.GroupName}'");
                }

                // Create a default path for the defaults file
                var lambdaDir = GetDirectoryPath(options, lambda);
                var defaultsPath = Path.Combine(lambdaDir, "autodeploy-defaults.json");

                // Create the directory if it doesn't exist
                Directory.CreateDirectory(Path.GetDirectoryName(defaultsPath));

                // Create a basic defaults file
                var defaults = new
                {
                    FunctionName = lambda.Name,
                    Description = $"Lambda function for {lambda.Name}",
                    Handler = lambda.Method,
                    Role = "arn:aws:iam::458678897586:role/lambda_basic_execution",
                    Runtime = lambda.Runtime ?? "dotnet6",
                    MemorySize = lambda.Memory ?? 256,
                    Timeout = lambda.Timeout ?? 30,
                    Environment = new
                    {
                        Variables = new Dictionary<string, string>()
                    }
                };

                // Write the defaults file
                using (var fs = new FileStream(defaultsPath, FileMode.Create, FileAccess.Write, FileShare.Read))
                {
                    using (var sw = new StreamWriter(fs))
                    {
                        sw.Write(JsonConvert.SerializeObject(defaults, Formatting.Indented));
                    }
                }

                Console.WriteLine($"Created defaults file for lambda '{lambda.Name}' at: {defaultsPath}");
                return defaultsPath;
            }
            catch (Exception ex) when (!(ex is ArgumentNullException) && !(ex is InvalidOperationException))
            {
                // Wrap other exceptions with more context
                throw new Exception($"Error creating defaults file for lambda '{lambda?.Name ?? "unknown"}': {ex.Message}", ex);
            }
        }

        public static void PreloadDefaultsFiles(Options options, IEnumerable<LambdaSettings> lambdas)
        {
            Console.WriteLine("Preloading defaults files for all lambdas...");

            // Create a list of unique defaults paths
            var defaultsPaths = new HashSet<string>();
            var configPaths = new HashSet<string>();

            foreach (var lambda in lambdas)
            {
                try
                {
                    var defaultsPath = GetDefaultsPath(options, lambda);
                    var configPath = GetConfigPath(options, lambda);

                    defaultsPaths.Add(defaultsPath);
                    configPaths.Add(configPath);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Warning: Failed to get paths for lambda '{lambda.Name}': {ex.Message}");
                }
            }

            // Preload all defaults files sequentially to avoid file access conflicts
            foreach (var path in defaultsPaths)
            {
                try
                {
                    if (File.Exists(path) && !defaultsCache.ContainsKey(path))
                    {
                        string content = SafeReadFileAsync(path).GetAwaiter().GetResult();
                        defaultsCache[path] = content;
                        Console.WriteLine($"Preloaded defaults file: {path}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Warning: Failed to preload defaults file {path}: {ex.Message}");
                }
            }

            // Preload all config files sequentially to avoid file access conflicts
            foreach (var path in configPaths)
            {
                try
                {
                    if (File.Exists(path) && !configCache.ContainsKey(path))
                    {
                        string content = SafeReadFileAsync(path).GetAwaiter().GetResult();
                        configCache[path] = content;
                        Console.WriteLine($"Preloaded config file: {path}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Warning: Failed to preload config file {path}: {ex.Message}");
                }
            }

            Console.WriteLine($"Preloaded {defaultsCache.Count} defaults files and {configCache.Count} config files");
        }

        // Add this method to safely read a file with retries
        private static async Task<string> SafeReadFileAsync(string filePath, int maxRetries = 3, int retryDelayMs = 100)
        {
            int retryCount = 0;
            while (true)
            {
                try
                {
                    // Use FileShare.ReadWrite to prevent exclusive locks
                    using (var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                    using (var sr = new StreamReader(fs))
                    {
                        return await sr.ReadToEndAsync();
                    }
                }
                catch (IOException ex) when (retryCount < maxRetries)
                {
                    retryCount++;
                    Console.WriteLine($"Retry {retryCount}/{maxRetries} reading file {filePath}: {ex.Message}");
                    await Task.Delay(retryDelayMs * retryCount);
                }
                catch (Exception ex)
                {
                    throw new Exception($"Error reading file {filePath}: {ex.Message}", ex);
                }
            }
        }

        // Add this method to get defaults content with caching
        public static string GetDefaultsContent(Options options, LambdaSettings lambda)
        {
            var defaultsPath = GetDefaultsPath(options, lambda);

            // Try to get from cache first
            if (defaultsCache.TryGetValue(defaultsPath, out string content))
            {
                Console.WriteLine($"Using cached defaults for {lambda.Name}");
                return content;
            }

            // Not in cache, need to read from file
            try
            {
                // Use a semaphore to limit concurrent file access
                fileAccessSemaphore.Wait();
                try
                {
                    // Check cache again in case another thread loaded it while we were waiting
                    if (defaultsCache.TryGetValue(defaultsPath, out content))
                    {
                        return content;
                    }

                    // Read the file
                    content = SafeReadFileAsync(defaultsPath).GetAwaiter().GetResult();

                    // Add to cache
                    defaultsCache[defaultsPath] = content;

                    return content;
                }
                finally
                {
                    fileAccessSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error reading defaults file for lambda '{lambda.Name}': {ex.Message}", ex);
            }
        }

        // Add this method to get config content with caching
        public static string GetConfigContent(Options options, LambdaSettings lambda)
        {
            var configPath = GetConfigPath(options, lambda);

            // Try to get from cache first
            if (configCache.TryGetValue(configPath, out string content))
            {
                Console.WriteLine($"Using cached config for {lambda.Name}");
                return content;
            }

            // Not in cache, need to read from file
            try
            {
                // Use a semaphore to limit concurrent file access
                fileAccessSemaphore.Wait();
                try
                {
                    // Check cache again in case another thread loaded it while we were waiting
                    if (configCache.TryGetValue(configPath, out content))
                    {
                        return content;
                    }

                    // Read the file
                    content = SafeReadFileAsync(configPath).GetAwaiter().GetResult();

                    // Add to cache
                    configCache[configPath] = content;

                    return content;
                }
                finally
                {
                    fileAccessSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error reading config file for lambda '{lambda.Name}': {ex.Message}", ex);
            }
        }

        // Add a retry method for File.Exists
        private static bool RetryFileExists(string path, int maxRetries = 3, int retryDelayMs = 500)
        {
            int retryCount = 0;
            while (true)
            {
                try
                {
                    return File.Exists(path);
                }
                catch (IOException ex) when (retryCount < maxRetries)
                {
                    retryCount++;
                    Console.WriteLine($"Retry {retryCount}/{maxRetries} checking if file exists {path}: {ex.Message}");
                    Thread.Sleep(retryDelayMs * retryCount);
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        // Add a retry method for reading files
        private static string RetryReadFile(string path, int maxRetries = 3, int retryDelayMs = 500)
        {
            int retryCount = 0;
            while (true)
            {
                try
                {
                    // Use FileShare.ReadWrite to prevent exclusive locks
                    using (var fs = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                    using (var sr = new StreamReader(fs))
                    {
                        return sr.ReadToEnd();
                    }
                }
                catch (IOException ex) when (retryCount < maxRetries)
                {
                    retryCount++;
                    Console.WriteLine($"Retry {retryCount}/{maxRetries} reading file {path}: {ex.Message}");
                    Thread.Sleep(retryDelayMs * retryCount);
                }
                catch (Exception ex)
                {
                    throw new Exception($"Error reading file {path}: {ex.Message}", ex);
                }
            }
        }
    }
}
