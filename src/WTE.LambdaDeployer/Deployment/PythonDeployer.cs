using System;
using System.Collections.Generic;
using System.IO;
using WTE.LambdaDeployer.Common.ConfigModel;
using WTE.LambdaDeployer.Common.Enums;
using WTE.LambdaDeployer.Reporting;

namespace WTE.LambdaDeployer.Deployment
{
    public static class PythonDeployer
    {
        private static readonly string ZipFileFormat = "{0}.zip";

        public static void DeployAll(Options options, LambdaConfig config = null)
        {
            BaseDeployer.DeployAll(options, LambdaLanguage.Python, "Python", DeploySingle, config);
        }

        public static List<LambdaSettings> GetLambdaSettings(Options options, LambdaConfig config = null)
        {
            return BaseDeployer.GetLambdaSettings(options, LambdaLanguage.Python, "Python", config);
        }

        public static void Deploy(Options options, LambdaConfig config = null, List<LambdaDefinition> specificLambdas = null)
        {
            BaseDeployer.Deploy(options, LambdaLanguage.Python, "Python", DeploySingle, config, specificLambdas);
        }

        public static void DeploySingle(LambdaSettings lambda, Options options)
        {
            try
            {
                if (lambda == null)
                {
                    throw new ArgumentNullException(nameof(lambda), "Lambda settings cannot be null");
                }

                Console.WriteLine("==============================================");
                Console.WriteLine("Deploying lambda {0} (Python)", lambda.Name);

                if (options.NotReally)
                {
                    BaseDeployer.LogLine(options, "(Not really) Updating lambda function...");
                    return;
                }

                var folderPath = BaseDeployer.GetDirectoryPath(options, lambda);
                var defaultsPath = BaseDeployer.GetDefaultsPath(options, lambda);
                var configPath = BaseDeployer.GetConfigPath(options, lambda);

                var currentDir = Directory.GetCurrentDirectory();
                System.IO.Directory.SetCurrentDirectory(folderPath);

                bool success = false;
                try
                {
                    BaseDeployer.LogLine(options, "Preparing configuration");
                    BaseDeployer.PrepareConfig(lambda, defaultsPath, configPath, options);
                    BaseDeployer.LogLine(options, "Checking if function {0} exists", lambda.Name);
                    ReportHelper.StartAction(lambda.Name, "Python", "$LATEST");

                    if (AmazonHelper.FunctionExists(lambda, options.AWSProfileName))
                    {
                        BaseDeployer.LogLine(options, "Function exists, updating code");
                        if (AmazonHelper.UpdateFunctionCode(lambda, string.Format(ZipFileFormat, lambda.Name), options.AWSProfileName))
                        {
                            BaseDeployer.LogLine(options, "Function code updated, updating function config");
                            if (AmazonHelper.UpdateFunctionConfiguration(lambda, configPath, options.AWSProfileName))
                            {
                                BaseDeployer.LogLine(options, "Function config successfully updated");
                                success = true;
                            }
                            else
                            {
                                throw new Exception("Failed to update function configuration");
                            }
                        }
                        else
                        {
                            throw new Exception("Failed to update function code");
                        }
                    }
                    else
                    {
                        BaseDeployer.LogLine(options, "Function does not exist, creating new function");
                        if (AmazonHelper.CreateFunction(lambda, string.Format(ZipFileFormat, lambda.Name), configPath, options.AWSProfileName))
                        {
                            BaseDeployer.LogLine(options, "Function successfully created");
                            lambda.IsNewFunction = true;
                            success = true;
                        }
                        else
                        {
                            throw new Exception("Failed to create function");
                        }
                    }

                    if (success)
                    {
                        if (!lambda.IsNewFunction)
                        {
                            Deployer.SetStage(lambda, options);
                        }
                    }
                    else
                    {
                        ReportHelper.FailAction("Failed to deploy function", "");
                        throw new Exception("Failed to deploy function");
                    }
                }
                catch (Exception ex)
                {
                    ReportHelper.FailAction(ex.Message, "");
                    BaseDeployer.LogLine(options, "Error deploying function: {0}", ex.Message);
                    throw new Exception($"Error deploying Python lambda '{lambda.Name}': {ex.Message}", ex);
                }
                finally
                {
                    System.IO.Directory.SetCurrentDirectory(currentDir);
                }
            }
            catch (Exception ex) when (!(ex is ArgumentNullException))
            {
                // Add lambda name to exception message
                throw new Exception($"Error deploying Python lambda '{lambda?.Name ?? "unknown"}': {ex.Message}", ex);
            }
        }
    }
}
