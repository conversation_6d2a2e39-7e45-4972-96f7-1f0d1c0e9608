using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;

namespace WTE.LambdaDeployer.Extensions
{
    public static class ZipArchiveExtensions
    {
        public static void CreateEntryFromAny(this ZipArchive archive, string sourceName, string entryName = "")
        {
            var fileName = Path.GetFileName(sourceName);
            if (File.GetAttributes(sourceName).HasFlag(FileAttributes.Directory))
            {
                archive.CreateEntryFromDirectory(sourceName, Combine(entryName, fileName));
            }
            else
            {
                archive.CreateEntryFromFile(sourceName, Combine(entryName, fileName), CompressionLevel.Fastest);
            }
        }

        public static void CreateEntryFromDirectory(this ZipArchive archive, string sourceDirName, string entryName = "")
        {
            string[] files = Directory.GetFiles(sourceDirName).Concat(Directory.GetDirectories(sourceDirName)).ToArray();
            //archive.CreateEntry(Combine(entryName, Path.GetFileName(sourceDirName)));
            foreach (var file in files)
            {
                var fileName = Path.GetFileName(file);
                archive.CreateEntryFromAny(file, entryName);
            }
        }

        private static string Combine(string path1, string path2)
        {
            return Path.Combine(path1, path2).Replace('\\', '/');
        }
    }
}