using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using WTE.LambdaDeployer.Common.ConfigModel;
using WTE.LambdaDeployer.Common.Enums;
using WTE.LambdaDeployer.Deployment;

namespace WTE.LambdaDeployer.Helpers
{
    public static class ConfigHelper
    {
        private static string LambdaConfigPath = "lambdaConfig.json";
        private static Dictionary<string, LambdaConfig> configCache = new Dictionary<string, LambdaConfig>();

        public static LambdaConfig GetConfig(Options options)
        {
            try
            {
                var path = Path.Combine(options.LambdaDirectory, LambdaConfigPath);

                // Check if config is already cached for this path
                if (configCache.TryGetValue(path, out LambdaConfig cachedConfig))
                {
                    return cachedConfig;
                }

                if (!File.Exists(path))
                {
                    Console.WriteLine($"ERROR: Config file not found at {path}");
                    return new LambdaConfig();
                }

                string json;
                // Use FileShare.ReadWrite to prevent file locking
                using (var fs = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                {
                    using (var sr = new StreamReader(fs))
                    {
                        json = sr.ReadToEnd();
                    }
                }

                var config = JsonConvert.DeserializeObject<LambdaConfig>(json);

                if (config.Groups == null)
                {
                    config = new LambdaConfig { Groups = new List<LanguageGroupConfig>(), Lambdas = config.Lambdas ?? new List<LambdaSettings>() };
                }

                // Cache the config for future use
                configCache[path] = config;

                return config;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR loading config: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                return new LambdaConfig();
            }
        }

        public static void ValidateConfig(Options options)
        {
            try
            {
                var config = GetConfig(options);
                if (config == null)
                {
                    throw new InvalidOperationException("Could not retrieve lambda configuration");
                }

                // Check for duplicate group names (case-insensitive)
                var groupNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                foreach (var group in config.Groups)
                {
                    if (!groupNames.Add(group.GroupName))
                    {
                        Console.WriteLine($"WARNING: Duplicate group name found: {group.GroupName}");
                    }
                }

                // Check that all lambdas have valid group names
                foreach (var lambda in config.Lambdas)
                {
                    if (string.IsNullOrEmpty(lambda.GroupName))
                    {
                        Console.WriteLine($"WARNING: Lambda {lambda.Name} has no group name specified");
                        continue;
                    }

                    if (!config.Groups.Any(g => string.Equals(g.GroupName, lambda.GroupName, StringComparison.OrdinalIgnoreCase)))
                    {
                        Console.WriteLine($"WARNING: Lambda {lambda.Name} references non-existent group {lambda.GroupName}");
                    }
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR validating config: {ex.Message}");
            }
        }

        public static LambdaConfig GetAllOfLanguage(Options options, LambdaLanguage language)
        {
            try
            {
                var config = GetConfig(options);
                if (config == null)
                {
                    throw new InvalidOperationException($"Could not retrieve lambda configuration for language {language}");
                }

                // Use case-insensitive comparison for language
                var groups = config.Groups
                    .Where(g => string.Equals(g.Language.ToString(), language.ToString(), StringComparison.OrdinalIgnoreCase))
                    .Select(g => g.GroupName)
                    .ToList();

                if (groups.Count == 0)
                {
                    Console.WriteLine($"Warning: No groups found for language {language}");
                    Console.WriteLine($"Available languages in groups: {string.Join(", ", config.Groups.Select(g => g.Language))}");
                }

                // Use case-insensitive comparison for group names
                var lambdas = config.Lambdas
                    .Where(l => groups.Any(g => string.Equals(g, l.GroupName, StringComparison.OrdinalIgnoreCase)))
                    .ToList();

                return new LambdaConfig
                {
                    Groups = config.Groups,
                    Lambdas = lambdas
                };
            }
            catch (Exception ex) when (!(ex is InvalidOperationException))
            {
                // Wrap other exceptions with more context
                throw new Exception($"Error getting all lambdas of language {language}: {ex.Message}", ex);
            }
        }

        public static LambdaConfig GetLanguageGroupLambdaConfig(Options options, string groupName)
        {
            var config = GetConfig(options);
            config.Lambdas = config.Lambdas.Where(l => l.GroupName == groupName).ToList();
            return config;
        }

        public static LanguageGroupConfig GetLanguageGroupConfig(Options options, string groupName)
        {
            var config = GetConfig(options);
            var langConfig = config.Groups.FirstOrDefault(l => l.GroupName == groupName);
            return langConfig;
        }

        public static LanguageGroupConfig GetLanguageGroupConfig(Options options, LambdaSettings lambda)
        {
            try
            {
                if (lambda == null)
                {
                    throw new ArgumentNullException(nameof(lambda), "Lambda settings cannot be null");
                }

                if (string.IsNullOrEmpty(lambda.GroupName))
                {
                    throw new InvalidOperationException($"Lambda '{lambda.Name}' has no group name specified");
                }

                var config = GetConfig(options);
                if (config == null || config.Groups == null)
                {
                    throw new InvalidOperationException("Could not retrieve lambda configuration or groups are null");
                }

                // Use case-insensitive comparison
                var group = config.Groups.FirstOrDefault(g =>
                    string.Equals(g.GroupName, lambda.GroupName, StringComparison.OrdinalIgnoreCase));

                if (group == null)
                {
                    throw new InvalidOperationException($"Group '{lambda.GroupName}' for lambda '{lambda.Name}' not found in configuration. Available groups: {string.Join(", ", config.Groups.Select(g => g.GroupName))}");
                }

                return group;
            }
            catch (Exception ex) when (!(ex is ArgumentNullException) && !(ex is InvalidOperationException))
            {
                // Wrap other exceptions with more context
                throw new Exception($"Error getting language group config for lambda '{lambda?.Name ?? "unknown"}' with group '{lambda?.GroupName ?? "unknown"}': {ex.Message}", ex);
            }
        }

        public static void ClearConfigCache()
        {
            configCache.Clear();
        }
    }
}
