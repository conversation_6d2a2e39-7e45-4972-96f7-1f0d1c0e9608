using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml;
using WTE.LambdaDeployer.Reporting.DOM;

namespace WTE.LambdaDeployer.Reporting
{
    public class ReportRenderer
    {
        private const string LambdaUrlFormat = "https://console.aws.amazon.com/lambda/home?region=us-east-1#/functions/{0}";
        private const string LambdaVersionUrlFormat =
            "https://console.aws.amazon.com/lambda/home?region=us-east-1#/functions/{0}/versions/{1}";
        private const string LambdaAliasUrlFormat =
            "https://console.aws.amazon.com/lambda/home?region=us-east-1#/functions/{0}/aliases/{1}";
        private DOMBuilder db;
        private IEnumerable<ActionEntry> rows;

        public ReportRenderer(DOMBuilderSettings settings = null)
        {
            db = new DOMBuilder(settings);
        }

        public string Render(AggregateReport report)
        {
            using (var stringWriter = new StringWriter())
            {
                RenderTo(report, stringWriter);
                return stringWriter.ToString();
            }
        }

        public void RenderTo(AggregateReport report, TextWriter output)
        {
            rows = report.ActionsComplete;
            db.MakeRoot(
                "html",
                db.head(
                    db.title("Lambda deployment report"),
                    //db.style("body { background: #212529; }"),
                    db.link(
                        db.Attr("rel", "stylesheet"),
                        db.Attr("href", "https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css"),
                        db.Attr("integrity", "sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm"),
                        db.Attr("crossorigin", "anonymous")
                    )
                ),
                db.body(
                    db.table(
                        db.Attr("class", "table"),
                        db.thead(
                            db.tr(
                                db.th("Lambda name"),
                                db.th("Alias"),
                                db.th("Language"),
                                db.th("Old version"),
                                db.th("New version"),
                                db.th("Modified at"),
                                db.th("Success"),
                                db.th("Error message")
                            )
                        ),
                        RenderTable()
                    ),
                    db.Text(string.Format("Deploy started at {0} and finished at {1}", report.DateStarted, report.DateFinished))
                )
            );
            db.SerializeTo(output);
        }

        private XmlNode RenderTable()
        {
            return db.tbody(
                rows.Select(
                    (row, index) =>
                        db.tr(
                            db.Attr("index", index),
                            db.td(db.a(db.Attr("href", string.Format(LambdaUrlFormat, row.FunctionName)), row.FunctionName)),
                            db.td(db.a(db.Attr("href", string.Format(LambdaAliasUrlFormat, row.FunctionName, row.Alias)), row.Alias)),
                            db.td(row.Language),
                            db.td(
                                db.a(
                                    db.Attr("href", string.Format(LambdaVersionUrlFormat, row.FunctionName, row.OldVersion)),
                                    row.OldVersion
                                )
                            ),
                            db.td(
                                db.a(
                                    db.Attr("href", string.Format(LambdaVersionUrlFormat, row.FunctionName, row.NewVersion)),
                                    row.NewVersion
                                )
                            ),
                            db.td(row.ModifiedAt.ToString()),
                            db.td(row.Success ? "Yes" : "No"),
                            db.td(row.ErrorMessage)
                        )
                )
            );
        }
    }
}