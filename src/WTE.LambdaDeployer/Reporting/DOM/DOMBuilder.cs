using System;
using System.Collections.Generic;
using System.IO;
using System.Xml;

namespace WTE.LambdaDeployer.Reporting.DOM
{
    public class DOMBuilder
    {
        private XmlDocument Document;
        private DOMBuilderSettings Settings;

        public DOMBuilder(DOMBuilderSettings settings = null)
        {
            if (settings == null)
            {
                settings = new DOMBuilderSettings();
            }
            Settings = settings;
            Document = new XmlDocument();
            Document.RemoveAll();
        }

        public void SerializeTo(TextWriter writer)
        {
            var xmlWriterSettings = new XmlWriterSettings { Indent = Settings.Indent, OmitXmlDeclaration = true };

            writer.WriteLine("<!DOCTYPE html>");
            using (var xmlWriter = XmlWriter.Create(writer, xmlWriterSettings))
            {
                Document.WriteContentTo(xmlWriter);
            }
        }

        public string Serialize()
        {
            using (var stringWriter = new StringWriter())
            {
                SerializeTo(stringWriter);
                return stringWriter.ToString();
            }
        }

        public XmlNode Text(string text)
        {
            return Document.CreateTextNode(text);
        }

        // List of all void tags, tags that cannot contain children
        private static HashSet<string> VoidTags = new HashSet<string>
        {
            "area",
            "base",
            "br",
            "col",
            "command",
            "embed",
            "hr",
            "img",
            "input",
            "keygen",
            "link",
            "meta",
            "param",
            "source",
            "track",
            "wbr",
        };

        private int AppendChildren(XmlElement node, IEnumerable<object> children, bool first = true)
        {
            var count = 0;
            var lowerName = node.Name.ToLower();
            foreach (var child in children)
            {
                if (child is null)
                {
                    continue;
                }
                else if (child is XmlAttribute)
                {
                    //node.AppendChild((XmlNode)child);
                    var attr = (XmlAttribute)child;
                    node.SetAttribute(attr.Name, attr.Value);
                }
                else if (child is XmlNode)
                {
                    node.AppendChild((XmlNode)child);
                    count += 1;
                }
                else if (child is string || child is int || child is decimal)
                {
                    var needCData = Settings.UseCData && (lowerName == "script" || lowerName == "style");
                    var content = child.ToString();
                    if (needCData)
                    {
                        // bleh, workaround for script sections
                        node.AppendChild(Document.CreateTextNode("/*"));
                        content = "*/\n" + content + "\n/*";
                        node.AppendChild(Document.CreateCDataSection(content));
                        node.AppendChild(Document.CreateTextNode("*/"));
                    }
                    else
                    {
                        node.AppendChild(Document.CreateTextNode(content));
                    }
                    count += 1;
                }
                else if (child is IEnumerable<XmlNode>)
                {
                    count += AppendChildren(node, (IEnumerable<object>)child, false);
                }
                else
                {
                    throw new Exception(string.Format("Invalid object received as a child: {0}", child));
                }
            }

            var isVoid = VoidTags.Contains(lowerName);
            if (!isVoid && count == 0)
            {
                // force closing tag with empty string child
                node.AppendChild(Document.CreateTextNode(""));
            }

            if (isVoid && count != 0)
            {
                // do not allow child nodes in void elements
                throw new Exception(string.Format("Void tags ({0}) cannot contain children", node.Name));
            }

            return count;
        }

        private void SetAttrs(XmlElement node, object attrs)
        {
            if (attrs is null)
                return;

            var type = attrs.GetType();
            var props = type.GetProperties();
            foreach (var prop in props)
            {
                var value = prop.GetValue(attrs);
                if (value is string || value is int || value is decimal)
                {
                    node.SetAttribute(prop.Name, value.ToString());
                }
                else if (value is bool)
                {
                    // boolean values set if certain property is present or not
                    if ((bool)value)
                    {
                        node.SetAttribute(prop.Name, string.Empty);
                    }
                }
                else
                {
                    throw new Exception(string.Format("Invalid value received as an attribute: {0}", value));
                }
            }
        }

        public void MakeRoot(string name, params object[] children)
        {
            var node = Create(name, children);
            Document.AppendChild(node);
        }

        public XmlNode Create(string name, params object[] children)
        {
            var node = Document.CreateElement(name);
            AppendChildren(node, children);
            return node;
        }

        public XmlAttribute Attr(string name, string value)
        {
            var attr = Document.CreateAttribute(name);
            attr.AppendChild(Document.CreateTextNode(value));
            return attr;
        }

        public XmlAttribute Attr(string name, int value)
        {
            return Attr(name, value.ToString());
        }

        public XmlAttribute Attr(string name, decimal value)
        {
            return Attr(name, value.ToString());
        }

        #region shorthand methods
        public XmlNode table(params object[] children) => Create("table", children);

        public XmlNode thead(params object[] children) => Create("thead", children);

        public XmlNode tbody(params object[] children) => Create("tbody", children);

        public XmlNode tr(params object[] children) => Create("tr", children);

        public XmlNode th(params object[] children) => Create("th", children);

        public XmlNode td(params object[] children) => Create("td", children);

        public XmlNode span(params object[] children) => Create("span", children);

        public XmlNode b(params object[] children) => Create("b", children);

        public XmlNode strong(params object[] children) => Create("strong", children);

        public XmlNode a(params object[] children) => Create("a", children);

        public XmlNode i(params object[] children) => Create("i", children);

        public XmlNode em(params object[] children) => Create("em", children);

        public XmlNode div(params object[] children) => Create("div", children);

        public XmlNode body(params object[] children) => Create("body", children);

        public XmlNode head(params object[] children) => Create("head", children);

        public XmlNode script(params object[] children) => Create("script", children);

        public XmlNode style(params object[] children) => Create("style", children);

        public XmlNode title(params object[] children) => Create("title", children);

        public XmlNode img(params object[] children) => Create("img", children);

        public XmlNode link(params object[] children) => Create("link", children);

        public XmlNode meta(params object[] children) => Create("meta", children);

        public XmlNode br(params object[] children) => Create("br", children);

        public XmlNode hr(params object[] children) => Create("hr", children);

        public XmlNode input(params object[] children) => Create("input", children);
        #endregion
    }
}