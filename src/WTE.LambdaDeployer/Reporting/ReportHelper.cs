using System;
using System.Collections.Concurrent;
using WTE.LambdaDeployer.Reporting.DOM;

namespace WTE.LambdaDeployer.Reporting
{
    public static class ReportHelper
    {
        private static readonly object _lockObject = new object();
        public static AggregateReport Report { get; set; } = new AggregateReport();
        private static ConcurrentDictionary<string, ActionEntry> CurrentActions { get; set; } = new ConcurrentDictionary<string, ActionEntry>();

        public static void StartDeployment(bool isPromotion)
        {
            lock (_lockObject)
            {
                Report.DateStarted = DateTime.UtcNow;
                Report.IsPromotion = isPromotion;
            }
        }

        public static void FinishDeployment()
        {
            lock (_lockObject)
            {
                Report.DateFinished = DateTime.UtcNow;
            }
        }

        public static void StartAction(string functionName, string language, string alias)
        {
            var actionKey = $"{functionName}:{language}:{alias}";
            var action = new ActionEntry()
            {
                FunctionName = string.IsNullOrEmpty(functionName) ? "" : functionName,
                Alias = string.IsNullOrEmpty(alias) ? "" : alias,
                Language = string.IsNullOrEmpty(language) ? "" : language,
            };

            CurrentActions.TryAdd(actionKey, action);
        }

        public static void SetOldVersion(string functionName, string language, string alias, string version)
        {
            var actionKey = $"{functionName}:{language}:{alias}";
            if (!CurrentActions.TryGetValue(actionKey, out var action))
            {
                StartAction(functionName, language, alias);
                CurrentActions.TryGetValue(actionKey, out action);
            }

            action.OldVersion = version;
        }

        public static void UpdateToNewVersion(string functionName, string language, string alias, string oldVersion, string newVersion)
        {
            var actionKey = $"{functionName}:{language}:{alias}";
            if (!CurrentActions.TryGetValue(actionKey, out var action))
            {
                SetOldVersion(functionName, language, alias, oldVersion);
                CurrentActions.TryGetValue(actionKey, out action);
            }

            action.NewVersion = newVersion;
            action.ModifiedAt = DateTime.UtcNow;
            action.Success = true;

            lock (_lockObject)
            {
                Report.ActionsComplete.Add(action);
            }

            ActionEntry removedAction;
            CurrentActions.TryRemove(actionKey, out removedAction);
        }

        public static void UpdateToNewVersion(string newVersion)
        {
            // This method is not thread-safe and should be updated
            // For now, we'll add a warning
            Console.WriteLine("WARNING: Using non-thread-safe UpdateToNewVersion method");
        }

        public static void FailAction(string errorMessage, string newVersion)
        {
            Console.WriteLine($"Failed action: {errorMessage} {newVersion}");
        }

        public static void LogFullAction(ActionEntry action)
        {
            lock (_lockObject)
            {
                Report.ActionsComplete.Add(action);
            }
        }

        public static string GetPrint()
        {
            var settings = new DOMBuilderSettings() { Indent = true };
            var reporthtml = new ReportRenderer(settings).Render(Report);
            return reporthtml;
        }
    }
}
