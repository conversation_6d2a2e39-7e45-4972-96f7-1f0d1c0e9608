using System;
using System.Collections.Generic;
using System.Linq;
using WTE.LambdaDeployer.Common.ConfigModel;
using WTE.LambdaDeployer.Common.Enums;
using WTE.LambdaDeployer.Deployment;
using WTE.LambdaDeployer.Helpers;
using LambdaDeployer = WTE.LambdaDeployer.Deployment.Deployer;

namespace WTE.LambdaDeployer
{
    public static class DeployerEntry
    {
        /// <summary>
        /// Entry point to AWS Lambda Deployer
        /// </summary>
        /// <param name="classes">List in function entry classes. Classes MUST BE IN EXECUTING ASSEMBLY.</param>
        /// <param name="name">Name of the function to deploy. ALL to deploy all found lambdas. </param>
        /// <param name="stage">Name of the deployment stage. Possible values: DEV, STAGE, PROD.</param>
        /// <param name="args">Arguments in standard format. Allowed arguments:
        /// -ln, --lambda-name [name]                                   : Specify lambda name. Qualified lambda names default to C#
        /// -fn, --folder-name [name]:[language]                        : Specify folder name. Default language - Nodejs. C# not supported.
        /// -dal, --deploy-all-language [language]                      : Deploy all lambdas for specified language. Cannot be used at the same time as manual lambda specification.
        /// -da, --deploy-all                                           : Deploy all available lambdas.
        /// -sa, --scan-alias                                           : Scan the stage alias instead of deploying anything.
        /// -cs, --custom-stage [stage]                                 : Use a custom stage name. WARNING: Ignores --yes-i-know-im-deploying-to-prod.
        /// -cbs, --custom-backup-stage [stage]                         : Specify a custom backup stage for a custom stage. Non-custom stage backups are defined in project.
        /// -v, --verbose                                               : Display lambda CLI output.
        /// -m, --memory [number]                                       : Set function memory (in MB).
        /// -t, --timeout [number]                                      : Set function timeout (in seconds).
        /// -ps, --promote-stage [stage]                                : Don't deploy; promote the stage specified in --promote-stage to the one specified in --stage.
        /// -nr, --not-really                                           : List actions to be taken without actually querying AWS.
        /// -av, --additional-variables [var]=[value] [var=value] ...   : Additional variables to add to lambdas.
        /// -s, --stage [stage]                                         : Specify target stage.
        /// -p, --profile [name]                                        : Specify the profile name to use with AWS.
        /// --yes-i-know-im-deploying-to-prod                           : Allow PROD and PROD_ROLLBACK deployment/promotion.
        /// -mpd, --max-parallel-deployments [number]            : Set maximum number of parallel deployments (default: 5).
        /// </param>
        public static void Deploy(List<string> args)
        {
            var options = ReadOptions(args);
            if (options.ScanAlias)
            {
                Deployer.ScanAlias(options);
            }
            else
            {
                // Preload all configurations in the main thread
                var config = ConfigHelper.GetConfig(options);
                ConfigHelper.ValidateConfig(options);

                // Preload all defaults files before starting deployment
                var allLambdasToPreload = GetAllLambdasToPreload(options, config);
                Console.WriteLine($"Preloading files for {allLambdasToPreload.Count} lambdas...");
                BaseDeployer.PreloadDefaultsFiles(options, allLambdasToPreload);

                // Now deploy with preloaded configurations
                Deployer.Deploy(options, config);
            }
        }

        public static Options ReadOptions(List<string> args)
        {
            var options = new Options();
            options.InterimSleepMs = 20000;
            bool gotTargetStage = false;
            bool gotSourceStage = false;
            bool gotBackupStage = false;
            string sourceStageName = null;
            string targetStageName = null;
            string backupStageName = null;

            for (int i = 0; i < args.Count; i++)
            {
                switch (args[i])
                {
                    case "-isms":
                    case "--interim-sleep-ms":
                        i++;
                        options.InterimSleepMs = int.Parse(args[i]);
                        break;
                    case "-ld":
                    case "--lambda-directory":
                        i++;
                        if (i > args.Count)
                        {
                            throw new Exception("You must provide the lambda directory.");
                        }
                        options.LambdaDirectory = args[i];
                        break;
                    case "-sa":
                    case "--scan-alias":
                        options.ScanAlias = true;
                        break;
                    case "-v":
                    case "--verbose":
                        options.Verbose = true;
                        break;
                    case "-nr":
                    case "--not-really":
                        options.NotReally = true;
                        break;
                    case "-ln":
                    case "--lambda-name":
                        i++;
                        if (i > args.Count)
                        {
                            throw new Exception("You must provide the lambda name.");
                        }
                        ParseLambdaNames(args[i], options);
                        break;
                    case "-da":
                    case "--deploy-all":
                        options.DeployAll = true;
                        options.DeployMode = true;
                        break;
                    case "-dal":
                    case "--deploy-all-language":
                        i++;
                        if (i > args.Count)
                        {
                            throw new Exception("You must provide the language to deploy.");
                        }
                        var lang = args[i].ToLower();
                        switch (lang)
                        {
                            case "c#":
                            case "cs":
                            case "csharp":
                                options.DeployAllLanguage.Add(LambdaLanguage.CSharp);
                                break;
                            case "python":
                            case "py":
                                options.DeployAllLanguage.Add(LambdaLanguage.Python);
                                break;
                            case "nodejs":
                            case "node.js":
                                options.DeployAllLanguage.Add(LambdaLanguage.Nodejs);
                                break;
                            default:
                                throw new Exception("Unrecognized language: " + args[i]);
                        }
                        options.DeployMode = true;
                        break;
                    case "-m":
                    case "--memory":
                        i++;
                        if (i > args.Count)
                        {
                            throw new Exception("You must provide the memory limit.");
                        }
                        options.Memory = int.Parse(args[i]);
                        break;
                    case "-ps":
                    case "--promote-stage":
                        if (gotSourceStage)
                        {
                            throw new Exception("Only one --promote-stage value is allowed.");
                        }
                        gotSourceStage = true;
                        i++;
                        if (i > args.Count)
                        {
                            throw new Exception("You must provide a source stage.");
                        }
                        options.PromoteStage = true;
                        sourceStageName = args[i];
                        break;
                    case "-cs":
                    case "--custom-stage":
                        options.CustomStage = true;
                        i++;
                        if (i > args.Count)
                        {
                            throw new Exception("You must provide the stage value.");
                        }
                        targetStageName = args[i];
                        break;
                    case "-cbs":
                    case "--custom-backup-stage":
                        if (gotBackupStage)
                        {
                            throw new Exception("Only one --backup-stage value is allowed.");
                        }
                        gotBackupStage = true;
                        i++;
                        if (i > args.Count)
                        {
                            throw new Exception("You must provide the stage value.");
                        }
                        options.CustomStageBackup = true;
                        backupStageName = args[i];
                        break;
                    case "-t":
                    case "--timeout":
                        i++;
                        if (i > args.Count)
                        {
                            throw new Exception("You must provide the timeout value.");
                        }
                        options.Timeout = int.Parse(args[i]);
                        break;
                    case "-av":
                    case "--additional-variables":
                        try
                        {
                            while (i < args.Count - 1 && !args[i + 1].StartsWith('-'))
                            {
                                var av = args[i + 1].Split('=', 2).Select(CleanString).ToList();
                                if (av.Count != 2)
                                {
                                    throw new Exception(
                                        "Invalid additional variable format. Provide additional variables in format: [name]=[value]. Given value: "
                                            + string.Join(',', av)
                                    );
                                }
                                else
                                {
                                    options.AdditionalVariables.Add(av[0], av[1]);
                                }
                                i++;
                            }
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine("Additional variables error: " + e);
                        }
                        break;
                    case "-s":
                    case "--stage":
                        try
                        {
                            if (gotTargetStage && !options.CustomStage)
                            {
                                throw new Exception("Only one --stage value is allowed.");
                            }
                            gotTargetStage = true;
                            i++;
                            if (i > args.Count)
                            {
                                throw new Exception("You must provide the stage value.");
                            }
                            if (!options.CustomStage)
                            {
                                targetStageName = args[i].ToLower();
                            }
                        }
                        catch (Exception e)
                        {
                            targetStageName = "staging";
                            Console.WriteLine("Additional variables error: " + e);
                            ;
                        }
                        break;
                    case "-p":
                    case "--profile":
                        i++;
                        if (i > args.Count)
                        {
                            throw new Exception("You must provide the profile name.");
                        }
                        options.AWSProfileName = args[i];
                        break;
                    case "--yes-i-know-im-deploying-to-prod":
                        options.HighPrivilege = true;
                        break;
                    case "-mpd":
                    case "--max-parallel-deployments":
                        i++;
                        if (i > args.Count)
                        {
                            throw new Exception("You must provide the maximum number of parallel deployments.");
                        }
                        if (int.TryParse(args[i], out int maxParallel))
                        {
                            options.MaxParallelDeployments = maxParallel;
                        }
                        else
                        {
                            throw new Exception("Maximum parallel deployments must be a number.");
                        }
                        break;
                    case "-cmd":
                    case "--create-missing-defaults":
                        options.CreateMissingDefaults = true;
                        break;
                    case "-mp":
                    case "--max-parallel":
                        i++;
                        if (i >= args.Count)
                        {
                            throw new Exception("You must provide a value for max parallel deployments.");
                        }
                        options.SetMaxParallelDeployments(args[i]);
                        break;

                    default:
                        throw new Exception("Unknown flag: " + args[i]);
                }
            }

            if (!options.CustomStage)
            {
                options.TargetStage = gotTargetStage ? Enum.Parse<EnvironmentStage>(targetStageName) : EnvironmentStage.NONE;
                options.SourceStage = gotSourceStage ? Enum.Parse<EnvironmentStage>(sourceStageName) : EnvironmentStage.NONE;
            }
            else
            {
                options.CustomStageTargetName = targetStageName;
                options.CustomStageSourceName = sourceStageName;
                options.CustomStageBackupName = backupStageName;
            }

            if (options.LambdasToDeploy.Count == 0 && options.DeployAllLanguage.Count == 0 && !options.DeployAll && !options.ScanAlias)
            {
                throw new Exception("You must define at least one lambda to deploy.");
            }

            if (options.DeployAllLanguage.Count > 0) // No manual specification alongside full-language
            {
                options.LambdasToDeploy.Clear();
            }

            if (
                (options.TargetStage == EnvironmentStage.prod || options.TargetStage == EnvironmentStage.prod_rollback)
                && !options.HighPrivilege
                && !options.ScanAlias
            )
            {
                throw new Exception(
                    (options.NotReally ? "(Not really) " : "")
                        + "You need high privilege (--yes-i-know-im-deploying-to-prod) to deploy/upgrade PROD/PROD_ROLLBACK."
                );
            }

            if (
                options.PromoteStage
                && (
                    (!options.CustomStage && (options.TargetStage == EnvironmentStage.NONE || options.SourceStage == EnvironmentStage.NONE))
                    || options.CustomStage
                        && (
                            string.IsNullOrWhiteSpace(options.CustomStageSourceName)
                            || string.IsNullOrWhiteSpace(options.CustomStageTargetName)
                        )
                )
            )
            {
                throw new Exception("Neither source nor target stage can be \"NONE\" when promoting a stage.");
            }

            ConfigHelper.ValidateConfig(options);

            return options;
        }

        public static string CleanString(string v)
        {
            var res = v.Trim();
            if (res.StartsWith('"') && res.EndsWith('"') && res.Length > 2)
            {
                res = res.Substring(1, res.Length - 2);
            }
            else
            {
                if (res.StartsWith('\'') && res.EndsWith('\'') && res.Length > 2)
                {
                    res = res.Substring(1, res.Length - 2);
                }
            }

            return res;
        }

        private static List<LambdaSettings> GetAllLambdasToPreload(Options options, LambdaConfig config)
        {
            var allLambdasToPreload = new List<LambdaSettings>();

            if (options.DeployAll)
            {
                // Add all lambdas from all languages
                allLambdasToPreload.AddRange(config.Lambdas);
            }
            else if (options.DeployAllLanguage.Count > 0)
            {
                // Add lambdas from specified languages
                foreach (var lang in options.DeployAllLanguage)
                {
                    var langConfig = ConfigHelper.GetAllOfLanguage(options, lang);
                    if (langConfig != null && langConfig.Lambdas != null)
                    {
                        allLambdasToPreload.AddRange(langConfig.Lambdas);
                    }
                }
            }
            else
            {
                // Add only the specified lambdas
                foreach (var lambda in options.LambdasToDeploy)
                {
                    var ld = config.Lambdas.FirstOrDefault(l => l.Name == lambda.LambdaName);
                    if (ld != null)
                    {
                        allLambdasToPreload.Add(ld);
                    }
                }
            }

            return allLambdasToPreload;
        }
        // Add this method to the ReadOptions class or DeployerEntry class

        private static void ParseLambdaNames(string lambdaNameArg, Options options)
        {
            // Split by comma to support multiple lambda names in a single -ln argument
            var lambdaNames = lambdaNameArg.Split(',', StringSplitOptions.RemoveEmptyEntries);

            foreach (var name in lambdaNames)
            {
                var trimmedName = name.Trim();
                if (string.IsNullOrEmpty(trimmedName))
                    continue;

                LambdaLanguage language = LambdaLanguage.Detect;
                var lambdaName = trimmedName;

                if (trimmedName.Split(':').Length > 1)
                {
                    var lambdalang = trimmedName.Split(':')[1];
                    lambdaName = trimmedName.Split(':')[0];

                    switch (lambdalang.ToLower())
                    {
                        case "nodejs":
                        case "node.js":
                        case "node":
                            language = LambdaLanguage.Nodejs;
                            break;
                        case "python":
                        case "py":
                            language = LambdaLanguage.Python;
                            break;
                        case "csharp":
                        case "cs":
                        case "c#":
                        default:
                            language = LambdaLanguage.CSharp;
                            break;
                    }
                }

                options.LambdasToDeploy.Add(new LambdaDefinition { LambdaName = lambdaName, Language = language });
            }

            options.DeployMode = true;
        }
    }
}
