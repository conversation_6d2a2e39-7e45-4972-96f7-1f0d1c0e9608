{"Groups": [{"GroupName": "csharp", "Language": "csharp", "Directory": "./src/WTE.Lambda.Functions", "DefaultsPath": "./src/WTE.Lambda.Functions/autodeploy-defaults.json", "ConfigPath": "./src/WTE.Lambda.Functions/autodeploy-defaults-with-ev.json"}, {"GroupName": "csharp2", "Language": "csharp", "Directory": "./src/WTE.Lambda.Functions", "DefaultsPath": "./src/WTE.Lambda.Functions/autodeploy-defaults.json", "ConfigPath": "./src/WTE.Lambda.Functions/autodeploy-defaults-with-ev.json"}, {"GroupName": "nodejs", "Language": "nodejs", "Directory": "./src/WTE.Lambda.Functions/NodeJS", "DefaultsPath": "./src/WTE.Lambda.Functions/NodeJS/autodeploy-defaults.json", "ConfigPath": "./src/WTE.Lambda.Functions/NodeJS/autodeploy-config.json"}, {"GroupName": "python", "Language": "python", "Directory": "./src/WTE.Lambda.Functions/Python", "DefaultsPath": "./src/WTE.Lambda.Functions/Python/autodeploy-defaults.json", "ConfigPath": "./src/WTE.Lambda.Functions/Python/autodeploy-config.json"}], "Lambdas": [{"Name": "LambdaDeployerPythonFunctionTest1", "GroupName": "python", "Runtime": "python2.7", "Include": ["package/dynamodb_json", "package/simplejson"], "EnvironmentVariables": {"hello": "var", "var": "hello"}}, {"Name": "LambdaDeployerNodejsFunctionTest1", "GroupName": "nodejs", "Runtime": "nodejs8.10", "Include": ["lambda.js", "wte-user-create-v2", "wte-list", "tedious"]}, {"Name": "LambdaDeployerNodejsFunctionTest2", "GroupName": "nodejs", "Runtime": "nodejs8.10", "Include": ["lambda.js", "wte-user-create-v2", "wte-list", "tedious"]}, {"Name": "lambda-deployer-cs-function-test-1", "GroupName": "csharp", "Memory": "256", "Framework": "net6.0", "Runtime": "dotnet6", "AssemblyName": "WTE.Lambda.Functions", "DeclaringType": "WTE.Lambda.Functions.CSharp.LambdaDeployerCsFunctionTest1", "Method": "LambdaDeployerCSFunctionTest1"}, {"Name": "lambda-deployer-cs-function-test-2", "GroupName": "csharp2", "Framework": "net6.0", "Runtime": "dotnet6", "AssemblyName": "WTE.Lambda.Functions", "DeclaringType": "WTE.Lambda.Functions.CSharp.LambdaDeployerCsFunctionTest2", "Method": "LambdaDeployerCSFunctionTest2", "EnvironmentVariables": {"hellovar1": "helloval1", "envvar1": "envval2", "envvar4": "envval10", "env3": "10.5"}}]}